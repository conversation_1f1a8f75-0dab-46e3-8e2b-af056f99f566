const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = 3001;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 日志中间件
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  console.log('Headers:', JSON.stringify(req.headers, null, 2));
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', JSON.stringify(req.body, null, 2));
  }
  console.log('---');
  next();
});

// 存储生成中的总结任务
const summaryTasks = new Map();

// 模拟重新生成总结接口
app.post('/gbi/planner/service/regenerate_summary', (req, res) => {
  console.log('🚀 收到重新生成总结请求');
  
  const { dataList, queryMsgId } = req.body;
  
  // 参数验证
  if (!dataList || !Array.isArray(dataList) || dataList.length === 0) {
    return res.status(400).json({
      code: '1',
      msg: '参数错误：dataList不能为空',
      data: null
    });
  }

  // 生成唯一的summaryKey
  const summaryKey = `summary_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
  
  // 模拟异步生成过程
  const task = {
    summaryKey,
    queryMsgId,
    dataList,
    status: 'processing',
    result: [],
    startTime: Date.now()
  };
  
  summaryTasks.set(summaryKey, task);
  
  // 模拟生成过程（3-8秒完成）
  const generateDuration = 3000 + Math.random() * 5000;
  setTimeout(() => {
    generateMockSummary(summaryKey);
  }, generateDuration);
  
  console.log(`✅ 生成summaryKey: ${summaryKey}`);
  
  res.json({
    code: '0',
    msg: '重新生成总结请求已提交',
    data: {
      summaryKey: summaryKey
    }
  });
});

// 模拟查询总结内容接口
app.post('/gbi/planner/service/get_summary', (req, res) => {
  console.log('🔍 收到查询总结内容请求');
  
  const { summaryKey } = req.body;
  
  if (!summaryKey) {
    return res.status(400).json({
      code: '1',
      msg: '参数错误：summaryKey不能为空',
      data: null
    });
  }
  
  const task = summaryTasks.get(summaryKey);
  
  if (!task) {
    return res.status(404).json({
      code: '1',
      msg: '未找到对应的总结任务',
      data: null
    });
  }
  
  console.log(`📊 查询任务状态: ${task.status}, 进度: ${task.result.length} 片段`);

  // 完全匹配真实planner接口的响应格式
  res.json({
    code: '0',
    msg: '',
    data: {
      result: task.result,
      status: task.status,
      id: `task_${task.summaryKey}`
    }
  });
});

// 生成模拟总结内容
function generateMockSummary(summaryKey) {
  const task = summaryTasks.get(summaryKey);
  if (!task) return;

  console.log(`🎯 开始生成总结内容: ${summaryKey}`);

  // 模拟真实接口的细粒度分片 - 参考真实接口响应格式
  const summaryParts = [
    "", "20", "24", "年6月1", "6日", "广东风能", "发电量为", "50", "33.2", "4。",
    "根据", "当前", "图表", "数据", "分析，", "在所", "选时间", "范围内，",
    "数据", "呈现出", "明显的", "趋势", "变化。", "主要", "指标", "表现为：",
    "总体", "增长率", "达到", "15.3%，", "其中", "核心", "业务", "贡献了",
    "主要", "增长", "动力。", "建议", "继续", "关注", "关键", "指标的",
    "变化", "趋势，", "以便", "及时", "调整", "业务", "策略。"
  ];

  // 模拟逐步生成过程
  let currentIndex = 0;

  const generateNext = () => {
    if (currentIndex < summaryParts.length) {
      task.result.push({
        sentence_id: currentIndex,
        content: summaryParts[currentIndex]
      });

      currentIndex++;

      console.log(`📝 生成片段 ${currentIndex}/${summaryParts.length}: "${summaryParts[currentIndex - 1]}"`);

      // 每个片段间隔200-800ms，模拟真实的流式生成
      const nextDelay = 200 + Math.random() * 600;
      setTimeout(generateNext, nextDelay);
    } else {
      // 生成完成
      task.status = 'end';
      console.log(`✅ 总结生成完成: ${summaryKey}`);

      // 5分钟后清理任务
      setTimeout(() => {
        summaryTasks.delete(summaryKey);
        console.log(`🗑️ 清理任务: ${summaryKey}`);
      }, 5 * 60 * 1000);
    }
  };

  // 开始生成第一个片段
  setTimeout(generateNext, 1000);
}

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    activeTasks: summaryTasks.size
  });
});

// 获取所有任务状态（调试用）
app.get('/debug/tasks', (req, res) => {
  const tasks = Array.from(summaryTasks.entries()).map(([key, task]) => ({
    summaryKey: key,
    status: task.status,
    resultCount: task.result.length,
    startTime: new Date(task.startTime).toISOString()
  }));
  
  res.json({
    totalTasks: summaryTasks.size,
    tasks: tasks
  });
});

// 清理所有任务（调试用）
app.delete('/debug/tasks', (req, res) => {
  const count = summaryTasks.size;
  summaryTasks.clear();
  res.json({
    message: `已清理 ${count} 个任务`
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    code: '1',
    msg: `接口不存在: ${req.method} ${req.path}`,
    data: null
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('❌ 服务器错误:', err);
  res.status(500).json({
    code: '1',
    msg: '服务器内部错误',
    data: null
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Planner Mock Server 启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔧 调试接口:`);
  console.log(`   - 健康检查: GET http://localhost:${PORT}/health`);
  console.log(`   - 查看任务: GET http://localhost:${PORT}/debug/tasks`);
  console.log(`   - 清理任务: DELETE http://localhost:${PORT}/debug/tasks`);
  console.log(`📋 Planner接口:`);
  console.log(`   - 重新生成总结: POST http://localhost:${PORT}/gbi/planner/service/regenerate_summary`);
  console.log(`   - 查询总结内容: POST http://localhost:${PORT}/gbi/planner/service/get_summary`);
  console.log('---');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 正在关闭服务器...');
  process.exit(0);
});
