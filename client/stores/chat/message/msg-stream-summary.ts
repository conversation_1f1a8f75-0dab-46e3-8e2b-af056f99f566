/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-30
 * @Description: 流式总结消息
 */

import {IMessageBase, MessageBase} from './msg-base';
import {action, decorate, observable, computed} from 'mobx';
import {safeJsonParse} from '../../../utils/gbi-utils/gbi-utils';

type StreamStatus = 'writing' | 'end';

interface StreamDataItem {
  sentence_id: number;
  content: string;
}

interface StreamContent {
  result: StreamDataItem[];
  status: StreamStatus;
}

const defaultStreamData: StreamContent = {result: [], status: 'writing'};

export class MsgStreamSummary extends MessageBase {
  /** 流式数据内容 */
  streamData: string[];
  /** 当前状态 */
  status: StreamStatus = 'writing';
  /** 返回的原始流式数据 */
  streamResult: StreamDataItem[];

  /** 初始化消息 */
  initMessage = action((msgData: IMessageBase, initData?: any) => {
    this.update(msgData);

    if (msgData.msgContent) {
      const {result, status} = safeJsonParse(this.msgContent, defaultStreamData);
      this.status = status;
      this.streamResult = result;
      this.streamData = result.map((item) => item.content || '');
    }
  }).bind(this);

  /** 更新当前消息 - 关键方法，用于实时更新流式内容 */
  updateMessage = action((msgData: IMessageBase) => {
    const {result, status} = safeJsonParse(msgData.msgContent, defaultStreamData);
    if ('end' === this.status) {
      return;
    }
    this.status = status;
    this.streamResult = result;
    this.streamData = result.map((item) => item.content || '');
    this.msgContent = msgData.msgContent;
  }).bind(this);

  /** 手动更新流式数据 - 用于轮询时更新 */
  updateStreamData = action((streamResult: StreamDataItem[], status: StreamStatus = 'writing') => {
    console.log('🔄 MsgStreamSummary.updateStreamData 被调用:', {
      currentStatus: this.status,
      newStatus: status,
      resultLength: streamResult.length,
      msgId: this.msgId
    });

    if ('end' === this.status) {
      console.log('⚠️ 消息已完成，跳过更新');
      return;
    }

    this.status = status;
    this.streamResult = streamResult;
    this.streamData = streamResult.map((item) => item.content || '');

    // 更新msgContent以保持数据一致性
    this.msgContent = JSON.stringify({
      result: streamResult,
      status: status
    });

    console.log('✅ 流式数据已更新:', {
      streamDataLength: this.streamData.length,
      fullContent: this.fullContent.substring(0, 50) + '...',
      status: this.status
    });
  }).bind(this);

  get streamFinished() {
    return this.status === 'end';
  }

  get fullContent() {
    return this.streamData.join('');
  }
}

decorate(MsgStreamSummary, {
  streamData: observable.ref,
  status: observable,
  streamResult: observable.ref,
  streamFinished: computed,
  fullContent: computed
});
