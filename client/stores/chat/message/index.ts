/*
 * @Author: den<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-20 18:43:42
 * @Description: 消息类的导入导出汇总
 */
import {MessageFactoryInstance} from './msg-factory';
import {MessageBase, IMessageBase} from './msg-base';
import {MsgChartResult} from './msg-chart-result';
import {MsgChartInsight} from './msg-chart-insight';
import {MsgQueryRecommend} from './msg-query-recommend';
import {MsgStream} from './msg-stream';
import {MsgChartStatistics} from './msg-chart-statistics';
import {MsgClarify} from './msg-clarify';
import {MsgClarifySelect} from './msg-clarify-select';
import {MsgModelRefs} from './msg-model-refs';
import {MsgModelTemplate} from './msg-model-template';
import {MsgCacheHits} from './msg-cache-hits';
import {MsgUserRequestPrediction} from './msg-request-prediction';
import {MsgAttributeClarify} from './msg-attribute-clarify';
import {MsgAttributeQuery} from './msg-attribute-query';
import {MsgAttributeResult} from './msg-attribute-result';
import {MsgStreamSummary} from './msg-stream-summary';

export type Message =
  | MessageBase
  | MsgChartInsight
  | MsgQueryRecommend
  | MsgStream
  | MsgChartStatistics
  | MsgClarify
  | MsgClarifySelect
  | MsgModelRefs
  | MsgModelTemplate
  | MsgCacheHits
  | MsgUserRequestPrediction
  | MsgAttributeClarify
  | MsgAttributeQuery
  | MsgAttributeResult
  | MsgStreamSummary;

export {
  IMessageBase,
  MessageBase,
  MsgChartInsight,
  MsgQueryRecommend,
  MsgStream,
  MessageFactoryInstance,
  MsgChartStatistics,
  MsgChartResult,
  MsgClarifySelect,
  MsgClarify,
  MsgModelRefs,
  MsgModelTemplate,
  MsgCacheHits,
  MsgUserRequestPrediction,
  MsgAttributeClarify,
  MsgAttributeQuery,
  MsgAttributeResult
};

const registerConfig = [
  {
    msgFormat: ['chart_insight', 'chart_init_recommend', 'chart_summary', 'chart_data_statistics'],
    msgClass: MsgChartInsight
  },
  {
    msgFormat: ['chart_result_json', 'chart_result_prediction_json'],
    msgClass: MsgChartResult
  },
  {
    msgFormat: ['chart_data_statistics'],
    msgClass: MsgChartStatistics
  },
  {
    msgFormat: ['user_clarify_select_json'],
    msgClass: MsgClarifySelect
  },
  {
    msgFormat: ['clarify_json'],
    msgClass: MsgClarify
  },
  {
    msgFormat: ['data_model_refs'],
    msgClass: MsgModelRefs
  },
  {
    msgFormat: ['query_recommend_result'],
    msgClass: MsgQueryRecommend
  },
  {
    msgFormat: ['stream_text', 'stream_text_reject', 'stream_text_error'],
    msgClass: MsgStream
  },
  {
    msgFormat: ['data_model_template'],
    msgClass: MsgModelTemplate
  },
  {
    msgFormat: ['msg-cache-hits'],
    msgClass: MsgCacheHits
  },
  {
    msgFormat: ['user_request_prediction'],
    msgClass: MsgUserRequestPrediction
  },
  // 归因澄清卡片消息格式
  {
    msgFormat: ['clarify_reason_index'],
    msgClass: MsgAttributeClarify
  },
  // 归因query
  {
    msgFormat: ['user_request_reason', 'user_clarify_select_reason_index'],
    msgClass: MsgAttributeQuery
  },
  // 归因结果
  {
    msgFormat: ['chart_result_reason_json'],
    msgClass: MsgAttributeResult
  },
  // 流式总结
  {
    msgFormat: ['stream_summary'],
    msgClass: MsgStreamSummary
  }
];

// 根据配置注册消息
registerConfig.forEach(({msgFormat, msgClass}) => {
  msgFormat.forEach(format => {
    MessageFactoryInstance.registerMsgFormat(format, msgClass);
  });
});
