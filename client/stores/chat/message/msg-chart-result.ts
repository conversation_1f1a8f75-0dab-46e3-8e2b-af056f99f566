/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-19 20:11:41
 * @Description: 推荐的图表结果
 */

import {toJS} from 'mobx';
import * as dayjs from 'dayjs';
import sugar_t from '../../../utils/i18n';
import {IMessageBase, MessageBase} from './msg-base';
import {get, post} from '../../../utils/fetch';
import {observable, decorate, action, computed, flow} from 'mobx';
import {IChartData, IChartResultJson, ILLMJsonParams} from '../types/data-insight-types';
import {safeJsonParse} from '../../../utils/gbi-utils/gbi-utils';
import {generateId} from '../../../utils/frontend-id';
import {IChartWidget, getChartByType, getChartEigens} from '../../../widgets/charts/chart-manager';
import {<PERSON><PERSON><PERSON>, IChartEntity, IChartHub} from '../../chart';
import {ISugar} from '../../';
import {IDataModelEntity} from '../../data-model/data-model';
import {getChartLoadDataPostParam} from '../../chart-model';
import {getReqUrlAndData} from '../../chart-data';
import {getRouteParams} from '../../../utils/route-params';

import * as moment from 'moment';
import {extractDataFields, recommendChartEigen, updateChartUsingNewChartEigen} from '../../../utils/data-model-helper';
import {Recommendation} from '../../../utils/chart-recommend-rule';
import {getChartData} from './utils';
import {iframeChartDataMock} from '../iframe-editor/iframe-mock';
import {getGbiIframeShareParam} from '../../../utils/gbi-utils/gbi-iframe';
import {getConditionsKTV} from '../../../utils/condition-helper';

import {IDataModelTemplate} from './types';

import {GBI_ICLHUB_PREFIX} from '../../../utils/api-prefix';

import isEqual = require('lodash/isEqual');

export const chartTypeMap = {
  table: 'table',
  summary: 'summary',
  bar: 'bar',
  pie: 'pie',
  line: 'line',
  indicator: 'kanbans',
  ranking: 'ranking',
  map: 'map',
  crossPivot: 'table'
};

// 数据消费支持的图表类型
export const DataInsightChartList = Object.values(chartTypeMap);

export const linkMap: PlainObject = {
  equal: '：',
  ineq: sugar_t('不等于 '),
  gt: sugar_t('大于 '),
  gte: sugar_t('大于等于 '),
  lt: sugar_t('小于 '),
  lte: sugar_t('小于等于 '),
  in: sugar_t('包含 '),
  include: sugar_t('包含 '),
  like: sugar_t('包含 '),
  start: sugar_t('开头是 '),
  end: sugar_t('结尾是 '),
  notIncluded: sugar_t('不包含 '),
  null: sugar_t('等于空 '),
  notNull: sugar_t('不等于空 ')
};

const transformData = (dataModel: PlainObject) => {
  const fields = dataModel['x-smart'].concat(dataModel['y-smart']);
  const keyList = ['x', 'x2', 'x3', 'y', 'y2', 'y3', 'color', 'size', 'value'];
  Object.keys(dataModel).forEach(key => {
    if (keyList.includes(key)) {
      dataModel[key] = dataModel[key]
        .map(item =>
          fields.find(field => {
            // 如果有聚合操作, 聚合操作需要一致
            if (item.aggregator) {
              return (
                (field.alias === item.alias && field.aggregator === item.aggregator) ||
                (field.id === item.id && field.aggregator === item.aggregator)
              );
            }
            return field.alias === item.alias || field.id === item.id;
          })
        )
        .filter(value => !!value);
    }
  });
};
export class MsgChartResult extends MessageBase {
  /** 正在加载数据中 */
  loading: boolean = false;
  /** 当前状态0代表成功 */
  status: number;
  /** 错误信息 */
  errMsg: string;
  /** 是否初始化完成 */
  hasInit: boolean = false;
  /** 图表数据信息 */
  chartResult: Partial<IChartResultJson>;
  /** 图表Id列表 */
  frontendIdList: string[] = [];
  /** 当前展示的图表chart */
  chartEntity: IChartEntity;
  /** 原始图表chart */
  originllmJsonParams: PlainObject;
  /** 当前展示的chartHub */
  charHub: IChartHub;
  /** 当前图标的llmjson数据 */
  llmJson: PlainObject = {};
  /** 模型信息 */
  dataModelTemplate?: IDataModelTemplate = undefined;
  /** 图表预测数据 */
  forecastResult: number[][] = [];
  /** 后端接口需要 */
  chartData: IChartData = {
    columns: [],
    rows: []
  };

  static getFiltersStr = (dataModel: IDataModelEntity, chartJson: IDataConfig) => {
    let filtersStr: any[] = [];
    let {
      dataModel: {filters = {}, having = {}}
    } = chartJson;
    const havingExprs = (having.exprs || []).map((filter: PlainObject) => {
      return {
        ...filter,
        having: true
      };
    });
    let connector = filters?.connect; // 多条件之间连接表达式
    let exprs = (filters.exprs || []).concat(havingExprs || []);
    exprs.forEach((expr: PlainObject) => {
      let filterVal = {name: '', value: '', link: '', data: expr, connector: undefined};
      let isHaving = !!expr?.having;
      const filter = expr?.filter;
      const dmId = filter?.id || '';
      const dimOrMea = dataModel?.config?.dimensions?.toJSON()?.[dmId] || dataModel?.config?.measures?.toJSON()?.[dmId];
      const alias = dimOrMea?.alias || dimOrMea?.field || dimOrMea?.convert?.label;
      if (dmId) {
        const dataType = filter?.searchParams?.type;
        if (dataType === 'date') {
          const {minDate = '', maxDate = ''} = filter?.searchParams?.date?.detail;
          if (minDate == maxDate) {
            filterVal.value = `${moment(minDate?.slice(1)).format('YYYY-MM-DD')}`;
            filterVal.link = '：';
          } else {
            filterVal.value = `${moment(minDate?.slice(1)).format('YYYY-MM-DD')}至${moment(maxDate?.slice(1)).format(
              'YYYY-MM-DD'
            )}`;
            filterVal.link = '：';
          }
        } else if (dataType === 'timestamp' || dataType === 'datetime') {
          const {minDate = '', maxDate = ''} = filter?.searchParams?.date?.detail;
          if (minDate == maxDate) {
            filterVal.value = `${minDate}`;
            filterVal.link = '：';
          } else {
            filterVal.value = `${minDate}至${maxDate}`;
            filterVal.link = '：';
          }
          if (minDate === '' || maxDate === '') {
            filterVal = {name: '', value: '', link: '', data: {}};
          }
        } else if (dataType === 'float' || dataType === 'int' || dataType === 'string') {
          if (filter.searchParams?.textMatch?.value?.length) {
            const val = filter.searchParams?.textMatch?.value || [];
            if (val.length > 0) {
              let value = '';
              val.forEach(obj => {
                filterVal.link = linkMap[obj.type] || '：';
                value += `${obj.value},`;
              });
              filterVal.value = value?.replace(/,$/, '');
            }
          }

          if (filter.searchParams?.listMatch?.list?.length) {
            const val = filter.searchParams?.listMatch?.list || [];
            let listVal = '';
            val.forEach((data: any) => {
              listVal += `${data},`;
            });
            if (listVal) {
              listVal = listVal.slice(0, -1);
            }
            filterVal.link = filter.searchParams?.listMatch.exclude ? sugar_t('不包含 ') : sugar_t('包含 ');
            filterVal.value = listVal;
          }
        }
      }

      if (filterVal && alias) {
        if (isHaving) {
          filterVal.link = sugar_t('聚合后') + filterVal.link;
        }
        filterVal.name = alias;
        if (connector && filtersStr.length > 0) {
          // 连接符只在非第一个条件时显示
          filterVal.connector = connector; // 与前置晒选条件的连接符
        }
        filtersStr.push(filterVal);
      }
    });
    return filtersStr;
  };

  /** 初始化消息 */
  initMessage = action((msgData: IMessageBase, initData?: any) => {
    this.update(msgData);
    if (msgData.msgContent) {
      let data = safeJsonParse(this.msgContent, {
        status: 0,
        errMsg: '',
        result: {title: '', llmJson: {}, dataModelTemplate: undefined, forecastResult: []}
      });
      this.chartResult = data.result as Partial<IChartResultJson>;
      this.status = data.status;
      this.errMsg = data.errMsg;
      this.llmJson = data.result?.llmJson;
      this.dataModelTemplate = data?.result?.dataModelTemplate;
      this.forecastResult = data?.result?.forecastResult || [];
    }
  }).bind(this);

  // 策略控制图标是否不能编辑
  get isReason() {
    return this.chartResult.chartDisable == 'true';
  }

  get isShow() {
    return this.status === 0;
  }

  /** 是否有可渲染的图表数据 */
  get hasChartData() {
    if (this.chartEntity.type === 'table' && this.charHub.data.data && !this.charHub.data.data.rows?.length) {
      return false;
    }
    return !!this.charHub.data?.data && !this.charHub.data?.data?.isDataEmpty;
  }

  getChartEntity = action((chartStore?: IChart) => {
    if (this.chartEntity) {
      return this.chartEntity;
    }
    // const chartType = {chartType: chartTypeMap[this.chartResult.chartType ?? 1], variantIndex: 0, defaultLimit: 1000};
    const chartType = {chartType: 'table', variantIndex: 0, defaultLimit: 1000};
    let chartWidget = getChartByType(chartType.chartType);
    const frontendId = generateId();
    this.frontendIdList.push(frontendId);
    const fields = this.chartResult.chartJson?.dataModel?.x.concat(this.chartResult.chartJson?.dataModel?.y || []);
    const dimAndMeas = {
      'limit': 1000,
      ...this.chartResult.chartJson?.dataModel,
      'x-smart': fields?.filter(item => item.type === 'd') || [],
      'y-smart': fields?.filter(item => item.type === 'm') || []
    };
    // 添加图表
    chartStore?.addDataInsightChart(
      dimAndMeas,
      this.chartResult.dataModelHash || '',
      `${this.msgId}`,
      frontendId,
      'report',
      chartType
    );
    const chartEntity = chartStore?.dataInsightChartList.data.find(val => val.frontendId == frontendId);
    // let chartTypeFinal = chartType?.chartType;
    chartEntity?.initChartConfig(chartWidget);
    chartEntity?.setConfig({
      ...chartEntity?.config,
      smartChart: {
        type: chartType.chartType
      }
    });
    const recommendConfig = {
      chartType: chartType?.chartType,
      variantIndex: chartType?.variantIndex,
      limit: dimAndMeas.limit,
      isErnie: true
    };
    // chartEntity?.recommendChartType(recommendConfig);

    const hub = chartStore?.getChartHub(frontendId);
    hub?.setChartConfigData(recommendConfig, true);
    this.chartEntity = chartEntity as IChartEntity;
    return chartEntity;
  }).bind(this);

  getChartHub = action(async (chartStore: IChart, frontendId: string) => {
    if (this.charHub) {
      return this.charHub;
    }
    const charHub = chartStore.getChartHub(frontendId);
    this.charHub = charHub;
    return charHub;
  }).bind(this);

  // 更新图表类型和数据
  updateChartAndData = action(async (root: any, pageHash: string, dataModel: IDataModelEntity) => {
    try {
      // 当前推荐图表类型
      const chartType = chartTypeMap[this.chartResult.chartType || ''];
      this.changeChart(dataModel, chartType);
      const data = await this.getChartData(root);
      this.charHub?.data.setSql(data?.sql || '');
      this.charHub?.data.setData(data?.data);
      const chartData = await getChartData(root, this.chartEntity);
      console.log('newCharData', chartData);
      if (chartData) {
        this.chartData = chartData;
      }
      // this.chartEntity.setDynamicName();
      this.chartEntity.autoUpdateName(null, dataModel, false, dataModel?.config);
      if (!this.originllmJsonParams) {
        const llmJson = await this.getLLmJson(root, true);
        this.originllmJsonParams = llmJson;
      }
    } finally {
      this.hasInit = true;
    }
  }).bind(this);

  // 获取图表数据
  getChartData = flow(function* (this: MsgChartResult, root: any) {
    try {
      this.loading = true;
      const dataModelHash = this.chartResult.dataModelHash;
      const loadData = getChartLoadDataPostParam(
        root,
        this.chartEntity,
        this.charHub,
        false,
        [],
        undefined,
        undefined,
        undefined,
        true
      );
      const reqData = getReqUrlAndData(this.chartEntity, 'editor', {conditions: []}, root);
      let llmJsonParam: Partial<ILLMJsonParams> = {...loadData, ...reqData.reqData};
      llmJsonParam = JSON.parse(JSON.stringify(llmJsonParam));
      transformData(llmJsonParam.dataConfig?.dataModel);
      // 如果是gbi模版编辑页
      if (this.session.workSpace.workListStore.gbiStore.pageType === 'gbi-editor') {
        return iframeChartDataMock;
      }

      let res: any;

      if (this.session.workSpace.workListStore.gbiStore.isEmbedIframe) {
        const shareId = this.session.workSpace.workListStore.gbiStore.gbiConfig.shareId;
        res = yield post(`/api/gbi-iframe/${dataModelHash}/chart-data/dataModelProxy`, {
          ...llmJsonParam,
          dataModelHash,
          // resourceHash: this.chartInsight.chart.hash
          resourceHash: this.chartResult.dataModelHash,
          ...getGbiIframeShareParam()
        });
      } else {
        res = yield post(`/api/gbi/${dataModelHash}/chart-data/dataModelProxy`, {
          ...llmJsonParam,
          dataModelHash,
          ...getGbiIframeShareParam(),
          // resourceHash: this.chartInsight.chart.hash
          resourceHash: this.chartResult.dataModelHash
        });
      }

      return res;
    } catch (error) {
      if (error.data?.status === 808) {
        return {
          sql: error.data.data.sql,
          data: null
        };
      }

      // 检测超时错误
      const isTimeout =
        ((error as any).data && (error as any).data.status === 2) ||
        ((error as any).response && (error as any).response.status === 504) ||
        ((error as any).code === 'ERR_BAD_RESPONSE' && error.message && error.message.includes('504')) ||
        (error.message && error.message.includes('Gateway Timeout')) ||
        (error.message && error.message.includes('timeout'));

      // 超时情况的特殊处理
      if (isTimeout) {
        (error as any).specialMessage = '查询超时，请稍后重试';
      }
      if (this.charHub && this.charHub.data) {
        this.charHub.data.setLoadError(error);
        this.charHub.data.setLoadState('error');
      }

      console.error(error);
      return null;
    } finally {
      this.loading = false;
    }
  }).bind(this);

  // 更新图表数据
  updateChartData = action(async (root: any, pageHash: string, dataModel: IDataModelEntity) => {
    try {
      const data = await this.getChartData(root, pageHash);
      this.charHub?.data.setSql(data.sql || '');
      this.charHub?.data.setData(data.data);
      const chartData = await getChartData(root, this.chartEntity);
      console.log('newCharData2', chartData);
      if (chartData) {
        this.chartData = chartData;
      }
      this.chartEntity?.autoUpdateName(null, dataModel, false, dataModel?.config);
    } finally {
      this.hasInit = true;
    }
  }).bind(this);

  // 获取推荐的表格
  getRecommendChartList = action((chartHub: IChartHub, dataModel: IDataModelEntity) => {
    const dimAndMeas = {
      'x-smart': this.chartEntity.dataConfig?.dataModel?.['x-smart'] || [],
      'y-smart': this.chartEntity.dataConfig?.dataModel?.['y-smart'] || []
    };
    // // 当前的维度和独立
    const d =
      dimAndMeas['x-smart']?.map((item: {id: string}) => {
        const node = dataModel?.config?.dimensions.get(item.id);
        const hNode = dataModel?.config?.dimensions.get(node?.hierarchyId || '');
        return {
          ...node,
          ...item,
          fieldName: 'x',
          hierarchyIndex: hNode?.pathIds.indexOf(item.id),
          subType: (node?.convert?.label || node?.convert.type || node?.dataType) as IDataFieldSubType,
          id: item.id
        };
      }) || [];
    const m =
      dimAndMeas['y-smart']?.map((item: {id: string}) => {
        const node = dataModel?.config?.measures.get(item.id);
        return {
          ...node,
          ...item,
          fieldName: 'y',
          id: item.id
        };
      }) || [];
    const dataFields = {d, m};
    let chartEigens = getChartEigens();
    // 选出推荐的图表特征
    let chartList = recommendChartEigen(dataFields, chartEigens, {isErnie: true});
    chartHub.setChartConfigData(
      {
        recommendChartEigenList: chartList
      },
      true
    );
    let recommendList = chartList.filter(
      item => item?.chartEigen?.forErnie && DataInsightChartList.includes(item?.chartEigen?.chartType || '')
    );
    return {recommendList, dataFields};
  }).bind(this);

  // 修改字段后自动切换图表
  changeChart = action((dataModel: IDataModelEntity, chartType?: string) => {
    let {recommendList, dataFields} = this.getRecommendChartList(this.charHub, dataModel);
    this.chartEntity.update({}, {type: 'smart-chart'});
    if (recommendList.length) {
      let firstRecommended = recommendList[0];
      let chosenChartType = firstRecommended.chartEigen.chartType!;
      if (chartType) {
        const chartSelected = recommendList.find(
          item => item.chartEigen.chartType === chartType && item?.chartEigen?.forErnie
        );
        if (chartSelected) {
          firstRecommended = chartSelected;
          chosenChartType = chartType;
        }
      }
      // 查找适配的推荐
      let newChartWidget = getChartByType(chosenChartType);
      if (newChartWidget) {
        updateChartUsingNewChartEigen(this.chartEntity, newChartWidget, dataFields, firstRecommended, true);
      }
    }
    this.chartEntity.update({}, {type: this.chartEntity?.config?.smartChart?.type});
    this.chartEntity.autoUpdateName(null, dataModel, false, dataModel?.config);
  }).bind(this);

  // 修改图表
  switchChart = action(
    (
      dataModel: IDataModelEntity,
      recommendation: Recommendation,
      chartWidget: IChartWidget,
      root: any,
      pageHash: string
    ) => {
      const oldDataFields = extractDataFields(this.chartEntity, dataModel);
      this.chartEntity.update({}, {type: 'smart-chart'});
      updateChartUsingNewChartEigen(this.chartEntity, chartWidget, oldDataFields, recommendation, true);
      this.chartEntity.update({}, {type: this.chartEntity?.config?.smartChart?.type});
      this.updateChartData(root, pageHash, dataModel);
    }
  ).bind(this);

  // 对比俩次请求参数是否一致，如果一致则返回true
  checkChartDataChange = action(async (root: ISugar) => {
    // 最新的修改后得到的llmJson参数
    const llmJsonParams = await this.getLLmJson(root, true);

    const llmJsonParamsJson = toJS(llmJsonParams);
    const originllmJsonParamsJson = toJS(this.originllmJsonParams);

    const preveParams = {type: originllmJsonParamsJson.type, dataConfig: originllmJsonParamsJson?.dataConfig || {}};
    const currentParams = {type: llmJsonParamsJson.type, dataConfig: llmJsonParamsJson?.dataConfig || {}};

    return isEqual(preveParams, currentParams);
  }).bind(this);

  // 获取图表数据
  getLLmJson = flow(function* (this: MsgChartResult, root: ISugar, needReturnReqParams?: boolean) {
    try {
      const dataModelHash = this.chartResult.dataModelHash;
      let llmJsonParam: Partial<ILLMJsonParams> = {};
      const chartHub = root.chart.getChartHub(this.chartEntity?.frontendId);
      const loadData = getChartLoadDataPostParam(
        root,
        this.chartEntity,
        chartHub,
        false,
        [],
        undefined,
        undefined,
        undefined,
        true
      );
      const conditionData = loadData || {condition: []};
      const reqData = getReqUrlAndData(this.chartEntity, 'editor', conditionData, root);
      llmJsonParam = JSON.parse(JSON.stringify({...llmJsonParam, ...loadData, ...reqData.reqData}));
      // 如果是获取原始图表数据，则不请求llmJson，直接返回请求参数
      if (needReturnReqParams) {
        return llmJsonParam;
      }
      let apiUrl = `${GBI_ICLHUB_PREFIX}/template/generateLLM`;
      const routeParams = getRouteParams();
      if (routeParams.pageType === 'gbi-iframe') {
        apiUrl = `/api/gbi-iframe/iclhub/template/generateLLM`;
      }
      const res = yield post(apiUrl, {
        ...llmJsonParam,
        dataModelHash,
        ...getGbiIframeShareParam()
      });
      let llmJson = res?.data?.llmJson || {};
      if (llmJson?.aggregator) {
        const newAggregator: PlainObject[] = [];
        const aggregator = llmJson?.aggregator;
        aggregator.forEach((item: PlainObject) => {
          const targetAggreator = newAggregator.find(
            (m: PlainObject) => m.type === item.type && m.field === item.field
          );
          if (!targetAggreator) {
            newAggregator.push(item);
          }
        });
        llmJson.aggregator = newAggregator;
      }
      delete llmJson.limit;
      delete llmJson.sort;
      delete llmJson.needGroupBy;

      // 去除所有的（去重计数文案）
      const removeCountDistinct = JSON.stringify(llmJson).replace(/\(去重计数\)/g, '');

      return JSON.parse(removeCountDistinct);
    } catch (error) {
      console.error(error);
    }
  }).bind(this);

  // 是否满足年-月-日数据
  getIsRightData() {
    const isLineBar = this.chartEntity.type === 'line' || this.chartEntity.type === 'bar';
    const xAxisData = this.charHub?.data?.data?.categories || [];
    const isEnoughData = xAxisData.length >= 10;
    const isDateData =
      xAxisData.length > 0 &&
      xAxisData.every((item: string) => {
        return moment(item, ['YYYY-MM-DD', 'YYYY/MM/DD'], true).isValid();
      });

    return {
      isLineBar,
      isEnoughData,
      isDateData,
      isRightData: isLineBar && isEnoughData && isDateData
    };
  }

  // 判断图表是否支持归因 坐标数据为时间类型
  get isSupportAttribute() {
    const xAxisData = this.charHub?.data?.data?.categories;
    if (!xAxisData) {
      return false;
    }
    return xAxisData.every((item: string) => dayjs(`${item}`).isValid);
  }

  // 获取归因默认参数
  get attributeDefaultParams() {
    if (!this.isSupportAttribute) {
      return;
    }

    const xAxisData = this.charHub?.data?.data?.categories;
    return {
      dateDimension: this.charHub?.data?.data?.categoriesName,
      reasonDate: xAxisData[xAxisData.length - 1],
      compareDate: xAxisData[xAxisData.length - 2]
    };
  }

  // 重新生成总结 
  regenerateSummary = flow(function* (this: MsgChartResult, root: any, pageHash: string, dataModel?: IDataModelEntity) {
    try {
    获取当前图表的llmJson和chartData 我从界面中看到的操作是的样的，图表选择了新的filter，更了chartdata，然后这个时候页面还请求了http://localhost:8085/api/iclhub/template/generateLLM
    这个参数返回了 我觉得这个参数重新生成的llmjson参数{
    "status": 0,
    "msg": "",
    "data": {
        "llmJson": {
            "dimensions": [
                "日期",
                "车型"
            ],
            "measures": [
                "订单数量"
            ],
            "filters": [
                {
                    "k": "配置",
                    "op": "in",
                    "v": [
                        "中配",
                        "低配"
                    ]
                },
                {
                    "k": "经销商",
                    "op": "notNull",
                    "v": [
                        ""
                    ]
                }
            ],
            "aggregator": [
                {
                    "type": "SUM",
                    "field": "订单数量"
                },
                {
                    "type": "SUM",
                    "field": "订单数量"
                }
            ],
            "dateAggregation": [
                {
                    "type": "YEAR-MONTH-DAY",
                    "field": "日期"
                },
                {
                    "type": "YEAR-MONTH-DAY",
                    "field": "日期"
                }
            ],
            "limit": 10000,
            "needGroupBy": true
        }
    }
} 
      const loadData = getChartLoadDataPostParam(root, this.chartEntity, this.charHub);
      const reqData = getReqUrlAndData(this.chartEntity, 'editor', {conditions: []}, root);
      let llmJsonParam: Partial<ILLMJsonParams> = {pageHash, ...loadData, ...reqData.reqData};
      llmJsonParam = JSON.parse(JSON.stringify(llmJsonParam));

      // 构建重新生成总结的请求数据
      const requestData = {
        msgId: this.msgId,
        dataList: [
          {
            dataModelHash: this.chartEntity.dataModelHash,
            llmJson: llmJsonParam,
            chartData: this.chartData
          }
        ]
      };

      // 调用重新生成总结接口
      const summaryResponse = yield post(`/api/gbi-consume/${pageHash}/regenerate-summary`, requestData);

      console.log('regenerateSummary响应:', summaryResponse);

      // 处理嵌套的响应结构
      if (summaryResponse.data && summaryResponse.data.data && summaryResponse.data.data.key) {
        // 返回查询key，用于后续轮询
        const summaryKey = summaryResponse.data.data.key;
        console.log('提取到summaryKey:', summaryKey);
        return summaryKey;
      } else {
        console.error('响应结构异常:', summaryResponse);
        throw new Error('重新生成总结失败：响应格式错误');
      }
    } catch (error) {
      console.error('重新生成总结失败:', error);
      throw error;
    }
  }).bind(this);

  // 查询总结内容
  querySummaryContent = flow(function* (this: MsgChartResult, pageHash: string, summaryKey: string) {
    try {
      const response = yield get(`/api/gbi-consume/${pageHash}/query-summary?key=${summaryKey}`);
      console.log('querySummaryContent响应:', response);

      // 处理嵌套的响应结构
      if (response.data && response.data.data) {
        return response.data.data;
      } else {
        console.error('查询响应结构异常:', response);
        throw new Error('查询总结内容失败：响应格式错误');
      }
    } catch (error) {
      console.error('查询总结内容失败:', error);
      throw error;
    }
  }).bind(this);
}

decorate(MsgChartResult, {
  status: observable,
  hasInit: observable,
  errMsg: observable,
  loading: observable,
  llmJson: observable,
  dataModelTemplate: observable,
  forecastResult: observable,
  chartData: observable,
  chartResult: observable,
  frontendIdList: observable,
  chartEntity: observable.ref,
  originllmJsonParams: observable,
  isReason: computed,
  isShow: computed,
  hasChartData: computed,
  charHub: observable.ref
});
