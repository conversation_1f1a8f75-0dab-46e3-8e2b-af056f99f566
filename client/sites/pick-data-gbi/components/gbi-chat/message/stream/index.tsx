/*
 * @Author: den<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-24 11:07:38
 * @Description: 数据流卡片
 */

import * as React from 'react';
import {observer} from 'mobx-react';
import {autorun} from 'mobx';
import {Markdown} from '../../../../../../widgets/gbi-ui';
import {MsgStream} from '../../../../../../stores/chat/message';
import CopyButton from '../../../../../../widgets/copy-button';
import {ICurrentGbiDataTableView, inject} from '../../../../../../stores/index';
import './index.scss';
import {IMessageCardWidget, IMessageCardWidgetProps, messageCardManager} from '..';
import {
  IMessageCardWidget as insightIMessageCardWidget,
  messageCardManager as insightMessageCardManager
} from '../../../../../data-insight/message/messageManager';
import {GBIContext} from '../../../../../../context';
import {GBIStore} from '../../../../../../stores/chat/GBIStore';
const LoadingPng = require('../../../../../../static/img/insight/insight-loading.png');

const {useRef, useEffect, useState, useContext} = React;

export const StreamMsg: React.FC<IMessageCardWidgetProps> = inject((sugar: any) => ({
  gbiDataTable: sugar?.gbiDataTable?.dataTableView
}))(
  observer(
    React.forwardRef(
      (props: IMessageCardWidgetProps & {gbiDataTable: ICurrentGbiDataTableView; showCopyBtn?: boolean}, ref) => {
        const contentRef = React.useRef<HTMLDivElement>(null);
        const {qa, message, gbiDataTable, showCopyBtn} = props;
        const msg = message as MsgStream;
        const globalContext = React.useContext(GBIContext);
        const gbiStore = globalContext.gbiStore as GBIStore;
        const currentIndexRef = useRef(0); // 当前处理到的索引
        const currentSentenceIndexRef = useRef(0); // 当前sentence中处理到的索引
        const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);
        const [streamText, setStreamText] = useState('');
        const [firstSentenceId, setFirstSentenceId] = useState<number>();
        const [lastRegenerateTimestamp, setLastRegenerateTimestamp] = useState<number>();
        const isLastQA = qa.session.isLastQA(qa);
        const isLastShowQA = qa.session.isLastShowQA(qa);
        // 当前卡片是否还在加载数据中
        const isChatLoading = isLastQA && !msg?.streamFinished && qa.session.chatStatus === 'process';

        const clearTimeoutIdRef = () => {
          if (timeoutIdRef.current) {
            clearTimeout(timeoutIdRef.current);
          }
        };

        useEffect(() => {
          let chatDisposer: () => void;

          // 检查是否是重新生成总结的情况：steamData为空但status为writing
          const isRegenerating = msg?.status === 'writing' && (!msg?.steamData || msg.steamData.length === 0);

          if (isChatLoading || isRegenerating) {
            chatDisposer = autorun(() => {
              const msgList = msg?.steamData || [];
              const firstId = msg?.streamResult?.[0]?.sentence_id;

              // 检查是否有重新生成时间戳
              let regenerateTimestamp: number | undefined;
              try {
                const msgContentObj = JSON.parse(msg?.msgContent || '{}');
                regenerateTimestamp = msgContentObj.regenerateTimestamp;
              } catch (e) {
                // 忽略JSON解析错误
              }

              clearTimeoutIdRef();

              // 如果检测到新的重新生成时间戳，强制重置状态
              if (regenerateTimestamp && regenerateTimestamp !== lastRegenerateTimestamp) {
                console.log('🔄 检测到重新生成，强制重置状态, timestamp:', regenerateTimestamp);
                setLastRegenerateTimestamp(regenerateTimestamp);
                currentIndexRef.current = 0;
                currentSentenceIndexRef.current = 0;
                setStreamText('');
                setFirstSentenceId(undefined);
                return;
              }

              // 如果是重新生成状态且数据为空，立即清空显示内容
              if (isRegenerating && msgList.length === 0) {
                setStreamText('');
                // 重置所有索引，确保下次有数据时能正常流式输出
                currentIndexRef.current = 0;
                currentSentenceIndexRef.current = 0;
                setFirstSentenceId(undefined);
                return;
              }

              // 兼容stream_text被替换的逻辑，如果firstSentenceId变了，则重置索引，清空组件内streamText
              if (firstId != null) {
                setFirstSentenceId(prev => {
                  if (prev !== firstId || isRegenerating) {
                    console.log('🔄 重置流式输出状态, 原firstId:', prev, '新firstId:', firstId, 'isRegenerating:', isRegenerating);
                    currentIndexRef.current = 0;
                    currentSentenceIndexRef.current = 0;
                    setStreamText('');
                  }
                  return firstId;
                });
              }

              const streamNextMessage = () => {
                if (currentIndexRef.current < msgList.length) {
                  clearTimeoutIdRef();
                  const currentSentence = msgList[currentIndexRef.current];
                  if (currentSentenceIndexRef.current < currentSentence.length) {
                    let text = msgList.slice(0, currentIndexRef.current).join('');

                    // 如果有图片，则跳过
                    if (currentSentence[currentSentenceIndexRef.current] === '!') {
                      const restWords = currentSentence.slice(currentSentenceIndexRef.current);
                      const match = restWords.match(/^!\[.*?\]\((.*?)\)/);
                      if (match) {
                        currentSentenceIndexRef.current += match[0].length;
                      }
                    }

                    currentSentenceIndexRef.current++;
                    text += currentSentence.slice(0, currentSentenceIndexRef.current);
                    setStreamText(text);
                    timeoutIdRef.current = setTimeout(streamNextMessage, 50);
                  } else {
                    currentIndexRef.current++; // 更新当前索引
                    setStreamText(msgList.slice(0, currentIndexRef.current).join(''));
                    currentSentenceIndexRef.current = 0; // 重置当前sentence索引
                    timeoutIdRef.current = setTimeout(streamNextMessage, 100);
                  }
                }
              };

              if (msg?.streamFinished) {
                setStreamText(msgList.join(''));
              } else {
                // 如果有未处理的消息，则继续处理
                if (currentIndexRef.current < msgList.length) {
                  streamNextMessage();
                }
              }
            });
          } else {
            let text = msg?.steamData?.join('') || '';
            setStreamText(text);
          }

          return () => {
            chatDisposer?.();
            clearTimeoutIdRef();
          };
        }, [msg.steamData, msg.status, msg.msgContent, isChatLoading]);

        const openDataModal = (hash: string) => {
          if (gbiStore.pageType === 'gbi-editor') {
            return;
          }
          gbiDataTable?.openModal({
            dataModelHash: hash
          });
        };
        // 检查是否是重新生成状态
        const isRegenerating = msg?.status === 'writing' && (!msg?.steamData || msg.steamData.length === 0);
        const shouldShowLoading = isChatLoading || isRegenerating;

        return (
          <div className="stream-msg">
            <div className="stream-msg-content" ref={ref || contentRef}>
              <Markdown
                className="stream-msg-content-markdown"
                content={streamText + (shouldShowLoading ? `![stream-loading](${LoadingPng})` : '')}
                openDataModal={openDataModal}
              />
            </div>
            {showCopyBtn && (
              <>
                {qa.isQAFinish && isLastShowQA && qa.isLastShowMsg(msg) && (
                  <CopyButton
                    textAlignLeft={true}
                    contentNode={
                      ((ref as React.RefObject<HTMLDivElement>)?.current || contentRef?.current) as HTMLDivElement
                    }
                  />
                )}
                {!isLastShowQA && qa.isLastShowMsg(msg) && (
                  <div className="stream-msg-content-hover">
                    <CopyButton
                      isHover={true}
                      contentNode={
                        ((ref as React.RefObject<HTMLDivElement>)?.current || contentRef?.current) as HTMLDivElement
                      }
                    />
                  </div>
                )}
              </>
            )}
          </div>
        );
      }
    )
  )
);

const config: IMessageCardWidget = {
  name: 'stream-message',
  msgType: ['stream_text', 'stream_text_reject', 'stream_text_error'],
  component: StreamMsg
};

messageCardManager.registerMessageCard(config);
insightMessageCardManager.registerMessageCard(config as insightIMessageCardWidget);
