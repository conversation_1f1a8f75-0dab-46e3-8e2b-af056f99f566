// 完整的重新生成总结函数 - 支持内容替换和打字机效果
const handleRegenerateSummary = async () => {
  if (isRegenerating) return;
  
  setIsRegenerating(true);
  try {
    const pageHash = gbiStore.pageHash;
    const summaryKey = await message.regenerateSummary(
      pageHash,
      undefined // dataModel参数可选
    );

    console.log('📋 regenerateSummary返回结果:', summaryKey);

    if (summaryKey) {
      console.log('✅ 获取到summaryKey:', summaryKey);
      console.log('🔄 开始流式总结...');
      
      // 查找并替换现有的stream_text消息
      console.log('🔄 查找现有的stream_text消息进行替换...');
      
      const currentQA = session.qaShowData.at(-1);
      if (!currentQA) {
        throw new Error('没有找到当前QA');
      }

      // 找到最后一个stream_text消息
      const existingStreamMsg = currentQA.msgList.slice().reverse().find((msg: any) => msg.msgFormat === 'stream_text') as MsgStream;
      
      if (existingStreamMsg) {
        console.log('✅ 找到现有的stream_text消息，将替换其内容, msgId:', existingStreamMsg.msgId);
        
        // 重置现有消息的状态，准备接收新内容
        existingStreamMsg.status = 'writing';
        existingStreamMsg.streamResult = [];
        existingStreamMsg.steamData = [];
        existingStreamMsg.msgContent = JSON.stringify({
          result: [],
          status: 'writing'
        });
        
        // 开始轮询并实时更新现有消息
        await startStreamingSummary(summaryKey, existingStreamMsg.msgId, pageHash, session);
      } else {
        console.log('❌ 没有找到现有的stream_text消息，创建新消息');
        
        // 创建新的MsgStream对象
        const streamMsg = new MsgStream(session, session.workSpace);
        const msgId = `summary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        // 初始化消息
        streamMsg.update({
          msgId: msgId,
          msgFormat: 'stream_text',
          source: 'assistant',
          msgContent: JSON.stringify({
            result: [],
            status: 'writing'
          })
        });
        
        // 调用initMessage来正确初始化流式数据
        streamMsg.initMessage({
          msgId: msgId,
          msgFormat: 'stream_text',
          source: 'assistant',
          msgContent: JSON.stringify({
            result: [],
            status: 'writing'
          })
        });

        // 添加到当前QA的消息列表
        currentQA.msgList.push(streamMsg);
        console.log('✅ 新流式总结消息已添加, msgId:', msgId);
        
        // 开始轮询并实时更新消息
        await startStreamingSummary(summaryKey, msgId, pageHash, session);
      }
    } else {
      console.error('❌ 未获取到summaryKey');
      throw new Error('未获取到summaryKey');
    }
  } catch (error) {
    console.error('重新生成总结失败:', error);
    alert('重新生成总结失败，请稍后重试');
  } finally {
    setIsRegenerating(false);
  }
};

// 流式总结轮询函数 - 支持打字机效果
const startStreamingSummary = async (summaryKey: string, msgId: string, pageHash: string, session: any) => {
  const maxAttempts = 30;
  let attempts = 0;
  let interval = 1000;
  const maxInterval = 3000;
  const backoffFactor = 1.2;

  const poll = async (): Promise<void> => {
    try {
      attempts++;
      console.log(`📡 第${attempts}次查询总结内容, key: ${summaryKey}`);
      
      const result = await message.querySummaryContent(pageHash, summaryKey);
      console.log('📊 查询结果:', result);

      if (result) {
        console.log('📊 处理查询结果:', result);
        
        // 找到对应的消息并更新
        const currentQA = session.qaShowData.at(-1);
        console.log('🔍 当前QA:', currentQA);
        console.log('🔍 查找msgId:', msgId);
        
        const streamMsg = currentQA?.msgList?.find((msg: any) => {
          console.log('🔍 检查消息:', msg.msgId, msg.msgFormat);
          return msg.msgId === msgId;
        }) as MsgStream;
        
        console.log('🔍 找到的流式消息:', streamMsg);
        
        if (streamMsg && streamMsg.updateMessage) {
          // 支持多种完成状态：'end' 或 'completed'
          const isCompleted = result.status === 'end' || result.status === 'completed';
          const status = isCompleted ? 'end' : 'writing';
          
          // 如果有streamResult，使用streamResult；否则从content构造
          let streamResult = result.streamResult || result.result || [];
          if (!streamResult.length && result.content) {
            // 如果没有streamResult但有content，构造一个简单的streamResult
            streamResult = [{sentence_id: 0, content: result.content}];
          }
          
          // 构造MsgStream期望的msgContent格式
          const msgContent = JSON.stringify({
            result: streamResult,
            status: status
          });
          
          // 使用updateMessage方法更新消息 - 这会触发打字机效果
          streamMsg.updateMessage({
            msgId: msgId,
            msgContent: msgContent,
            msgFormat: 'stream_text'
          });
          console.log(`🔄 更新流式消息, 片段数: ${streamResult.length}, 状态: ${status}`);
          console.log('🔍 更新后的steamData:', streamMsg.steamData);
          console.log('🔍 更新后的status:', streamMsg.status);
        }

        // 如果完成，停止轮询 - 支持多种完成状态
        if (result.status === 'end' || result.status === 'completed') {
          console.log('✅ 流式总结完成, 状态:', result.status);
          return;
        }
      }

      // 继续轮询
      if (attempts < maxAttempts) {
        interval = Math.min(interval * backoffFactor, maxInterval);
        setTimeout(poll, interval);
      } else {
        throw new Error('总结生成超时');
      }
    } catch (error) {
      console.error(`轮询第${attempts}次失败:`, error);
      if (attempts < maxAttempts) {
        interval = Math.min(interval * backoffFactor, maxInterval);
        setTimeout(poll, interval);
      } else {
        throw error;
      }
    }
  };

  await poll();
};
