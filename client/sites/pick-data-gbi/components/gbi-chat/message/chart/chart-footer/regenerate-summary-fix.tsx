// 修复后的重新生成总结函数
const handleRegenerateSummary = async () => {
  if (isRegenerating) return;
  
  setIsRegenerating(true);
  try {
    const pageHash = gbiStore.pageHash;
    const summaryKey = await message.regenerateSummary(
      pageHash,
      undefined // dataModel参数可选
    );

    console.log('📋 regenerateSummary返回结果:', summaryKey);

    if (summaryKey) {
      console.log('✅ 获取到summaryKey:', summaryKey);
      console.log('🔄 开始流式总结...');
      
      // 直接在前端创建流式总结消息
      console.log('📝 直接创建流式总结消息...');
      
      const currentQA = session.qaShowData.at(-1);
      if (!currentQA) {
        throw new Error('没有找到当前QA');
      }

      // 创建新的MsgStream对象
      const streamMsg = new MsgStream(session, session.workSpace);
      const msgId = `summary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 初始化消息
      streamMsg.update({
        msgId: msgId,
        msgFormat: 'stream_text',
        source: 'assistant',
        msgContent: JSON.stringify({
          result: [],
          status: 'writing'
        })
      });
      
      // 调用initMessage来正确初始化流式数据
      streamMsg.initMessage({
        msgId: msgId,
        msgFormat: 'stream_text',
        source: 'assistant',
        msgContent: JSON.stringify({
          result: [],
          status: 'writing'
        })
      });

      // 直接添加到当前QA的消息列表
      currentQA.msgList.push(streamMsg);
      console.log('✅ 流式总结消息已添加, msgId:', msgId);
      console.log('🔍 当前消息列表:', currentQA.msgList.map(m => ({id: m.msgId, format: m.msgFormat})));

      // 开始轮询并实时更新消息
      await startStreamingSummary(summaryKey, msgId, pageHash, session);
    } else {
      console.error('❌ 未获取到summaryKey');
      throw new Error('未获取到summaryKey');
    }
  } catch (error) {
    console.error('重新生成总结失败:', error);
    alert('重新生成总结失败，请稍后重试');
  } finally {
    setIsRegenerating(false);
  }
};
