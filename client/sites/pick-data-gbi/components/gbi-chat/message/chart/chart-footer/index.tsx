/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-27 14:34:54
 * @Description: 图表卡片底部区域
 */
import cx from 'classnames';
import sugar_t from '../../../../../../../utils/i18n';
import * as React from 'react';
import {observer} from 'mobx-react';
import ComTooltip from '../../../../../../../widgets/com-tooltip';
import {MsgChartResult, MsgStream} from '../../../../../../../stores/chat/message';
import {IAssist} from '../../../../../../../stores';
import './index.scss';
import {GBIContext} from '../../../../../../../context';
import {GBIStore} from '../../../../../../../stores/chat/GBIStore';
const CodePng = require('../../../../../../../static/img/data-insight/code.svg');
const ExportPng = require('../../../../../../../static/img/data-insight/export.svg');

export interface IChartFooter {
  /** 当前消息 */
  message: MsgChartResult;
  /** 展示sql */
  checkSQL: () => void;
  /** 导出 */
  exportData: () => void;
  assist?: IAssist;
  /** 是否是预测图表消息 */
  isPredictionMsg?: boolean;
  /** root对象 */
  root?: any;
}

export const ChartFooter: React.FC<IChartFooter> = observer(
  React.forwardRef((props: IChartFooter) => {
    const {checkSQL, exportData, message, assist, isPredictionMsg, root} = props;
    const globalContext = React.useContext(GBIContext);
    const gbiStore = globalContext.gbiStore as GBIStore;
    const [isRegenerating, setIsRegenerating] = React.useState(false);

    // 查看代码
    const handleCheckSQL = () => {
      assist?.clientLog?.('reportConsumptionViewCode');
      checkSQL();
    };

    // 导出数据
    const handleExportData = () => {
      assist?.clientLog?.('reportConsumptionExportData');
      exportData();
    };

    // 重新生成总结
    const regenerateSummary = async () => {
      const session = gbiStore.workSpaceListStore.currentWorkSpace.session;
      if (session.isLoading || isRegenerating) {
        console.log('⚠️ 正在处理中，忽略重复点击, isLoading:', session.isLoading, 'isRegenerating:', isRegenerating);
        return;
      }

      try {
        setIsRegenerating(true);
        console.log('🚀 开始重新生成总结...');

        // 强制重置session状态，确保能够重新开始
        session.update({chatStatus: 'idle'});
        console.log('🔄 重置session状态为idle');

        // 获取pageHash - 使用当前工作区的spaceId作为pageHash
        const pageHash = gbiStore.workSpaceListStore.currentWorkSpace.workSpaceInfo.workId || 'default';

        // 使用传入的root对象
        if (!root) {
          throw new Error('未找到root对象');
        }

        // 调用重新生成总结方法
        console.log('🚀 开始调用regenerateSummary...');
        const summaryKey = await message.regenerateSummary(
          root,
          pageHash,
          undefined // dataModel参数可选
        );

        console.log('📋 regenerateSummary返回结果:', summaryKey);

        if (summaryKey) {
          console.log('✅ 获取到summaryKey:', summaryKey);
          console.log('🔄 开始流式总结...');

          // 查找并替换现有的stream_text消息
          console.log('� 查找现有的stream_text消息进行替换...');

          const currentQA = session.qaShowData.at(-1);
          if (!currentQA) {
            throw new Error('没有找到当前QA');
          }

          // 找到最后一个stream_text消息
          const existingStreamMsg = currentQA.msgList.slice().reverse().find((msg: any) => msg.msgFormat === 'stream_text') as MsgStream;

          if (existingStreamMsg) {
            console.log('✅ 找到现有的stream_text消息，将替换其内容, msgId:', existingStreamMsg.msgId);

            // 先设置session状态为process，确保isChatLoading为true
            session.update({chatStatus: 'process'});
            console.log('🔄 设置session状态为process，启用打字机效果');

            // 立即清除老内容，显示加载状态
            existingStreamMsg.status = 'writing';
            existingStreamMsg.streamResult = [];
            existingStreamMsg.steamData = [];
            existingStreamMsg.msgContent = JSON.stringify({
              result: [],
              status: 'writing',
              regenerateTimestamp: Date.now() // 添加重新生成时间戳，确保触发重置
            });
          console.log('� 流式总结消息已创建, msgId:', existingStreamMsg.msgId);
            console.log('🧹 立即清除老内容完成');

            // 开始轮询并实时更新消息 第一次点击之后，情况内容，流失展示。再次点击之后，
            await startStreamingSummary(summaryKey, existingStreamMsg.msgId, pageHash, session);
          } else {
            console.log('❌ 没有找到现有的stream_text消息');
            throw new Error('没有找到现有的stream_text消息');

          }
        } else {
          console.error('❌ 未获取到summaryKey');
          throw new Error('未获取到summaryKey');
        }
      } catch (error) {
        console.error('重新生成总结失败:', error);
        alert('重新生成总结失败，请稍后重试');

        // 错误时也要重置session状态
        const session = gbiStore.workSpaceListStore.currentWorkSpace.session;
        session.update({chatStatus: 'error'});
        console.log('❌ 错误时重置session状态为error');
      } finally {
        setIsRegenerating(false);
        console.log('🔄 重置isRegenerating状态为false');
      }
    };

    // 流式总结轮询函数
    const startStreamingSummary = async (summaryKey: string, msgId: string, pageHash: string, session: any) => {
      const maxAttempts = 30;
      let attempts = 0;
      let interval = 1000;
      const maxInterval = 3000;
      const backoffFactor = 1.2;

      const poll = async (): Promise<void> => {
        try {
          attempts++;
          console.log(`📡 第${attempts}次查询总结内容, key: ${summaryKey}`);

          const result = await message.querySummaryContent(pageHash, summaryKey);
          console.log('📊 查询结果:', result);

          if (result) {
            console.log('📊 处理查询结果:', result);

            // 找到对应的消息并更新
            const currentQA = session.qaShowData.at(-1);
            console.log('🔍 当前QA:', currentQA);
            console.log('🔍 查找msgId:', msgId);

            const streamMsg = currentQA?.msgList?.find((msg: any) => {
              console.log('🔍 检查消息:', msg.msgId, msg.msgFormat);
              return msg.msgId === msgId;
            }) as MsgStream;

            console.log('🔍 找到的流式消息:', streamMsg);

            if (streamMsg && streamMsg.updateMessage) {
              // 支持多种完成状态：'end' 或 'completed'
              const isCompleted = result.status === 'end' || result.status === 'completed';
              const status = isCompleted ? 'end' : 'writing';

              // 如果有streamResult，使用streamResult；否则从content构造
              let streamResult = result.streamResult || result.result || [];
              if (!streamResult.length && result.content) {
                // 如果没有streamResult但有content，构造一个简单的streamResult
                streamResult = [{sentence_id: 0, content: result.content}];
              }

              // 构造MsgStream期望的msgContent格式
              const msgContent = JSON.stringify({
                result: streamResult,
                status: status
              });

              // 使用updateMessage方法更新消息
              streamMsg.updateMessage({
                msgId: msgId,
                msgContent: msgContent,
                msgFormat: 'stream_text'
              });
              console.log(`🔄 更新流式消息, 片段数: ${streamResult.length}, 状态: ${status}`);
              console.log('🔍 更新后的steamData:', streamMsg.steamData);
              console.log('🔍 更新后的status:', streamMsg.status);
            }

            // 如果完成，停止轮询 - 支持多种完成状态
            if (result.status === 'end' || result.status === 'completed') {
              console.log('✅ 流式总结完成, 状态:', result.status);

              // 重置session状态，停止打字机效果
              session.update({chatStatus: 'success'});
              console.log('🔄 设置session状态为success，停止打字机效果');
              return;
            }
          }

          // 继续轮询
          if (attempts < maxAttempts) {
            interval = Math.min(interval * backoffFactor, maxInterval);
            setTimeout(poll, interval);
          } else {
            throw new Error('总结生成超时');
          }
        } catch (error) {
          console.error(`轮询第${attempts}次失败:`, error);
          if (attempts < maxAttempts) {
            interval = Math.min(interval * backoffFactor, maxInterval);
            setTimeout(poll, interval);
          } else {
            throw error;
          }
        }
      };

      await poll();
    };

    const isHideCode = gbiStore.gbiConfig.hideProcessCode;

    // 预测图表消息，返回的操作按钮为空
    //
    if (isPredictionMsg) {
      return null;
    }

    return (
      <div className="gbi-chart-insight-footer">
        <div
          className={cx('gbi-chart-insight-footer-position', {
            'gbi-chart-insight-footer-position-hide-code': isHideCode
          })}
        >
          {!gbiStore.gbiConfig.hideProcessCode && (
            <ComTooltip placement="top" title={sugar_t('查看代码')}>
              <div
                onClick={handleCheckSQL}
                className={cx('gbi-chart-insight-footer-iconButton', {
                  'gbi-iframe-editor-active':
                    gbiStore.isIframeEditor && gbiStore.gbiConfig.activeFormItem === 'hideProcessCode'
                })}
              >
                <img src={CodePng} />
                <span>代码</span>
              </div>
            </ComTooltip>
          )}
          {!gbiStore.workSpaceListStore.currentWorkSpace.session.isLoading && !isRegenerating && (
            <ComTooltip placement="top" title="重新生成总结">
              <div
                onClick={regenerateSummary}
                className="gbi-chart-insight-footer-iconButton"
              >
                <span>重新生成总结123</span>
              </div>
            </ComTooltip>
          )}
          {isRegenerating && (
            <div className="gbi-chart-insight-footer-iconButton gbi-chart-insight-footer-loading">
              <span>生成中...</span>
            </div>
          )}
          <ComTooltip placement="top" title={sugar_t('导出数据')}>
            <div
              onClick={handleExportData}
              className="gbi-chart-insight-footer-iconButton gbi-chart-insight-footer-export"
            >
              <img src={ExportPng} />
              <span>导出567</span>
            </div>
          </ComTooltip>
        </div>
      </div>
    );
  })
);
