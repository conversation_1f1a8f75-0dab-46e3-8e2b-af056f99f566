.stream-summary-msg {
  position: relative;

  &:hover {
    .stream-summary-msg-content-hover {
      display: flex;
    }
  }
  
  .stream-summary-msg-content-hover {
    left: 0px;
    bottom: -32px;
    z-index: 1;
    height: 32px;
    display: none;
    position: absolute;
    align-items: center;
    justify-content: center;
    padding: 4px;
    border-radius: 8px;
    background-color: #fff;
    border: 1px solid #f5f6f8;
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.05);
  }
  
  .stream-summary-msg-content-markdown {
    span {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      color: #151b26;
      line-height: 26px;
      font-weight: 400;
    }

    & > span:not(:last-child) {
      margin-bottom: 12px;
    }

    & > ul {
      padding-left: 28px;
    }

    & > p:not(:last-child) {
      margin-bottom: 12px;
    }

    & > p {
      font-size: 14px;
    }

    & > ol {
      padding-left: 15px;

      & > li {
        margin-bottom: 12px;

        & > p {
          margin-bottom: 8px;
        }

        & > ul {
          & > li:not(:last-child) {
            margin-bottom: 8px;
          }
        }

        & > ul:not(:last-child) {
          margin-bottom: 8px;
        }
      }

      & > li {
        & > ul {
          padding-left: 14px;
        }
      }

      & > li:not(:last-child) {
        margin-bottom: 12px;
      }

      & > li:last-child {
        margin-bottom: 0px;
      }
    }

    & > ol:not(:last-child) {
      margin-bottom: 12px;
    }

    & > ol:last-child {
      margin-bottom: 0px;
    }
  }
}
