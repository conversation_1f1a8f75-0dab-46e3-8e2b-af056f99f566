/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-30
 * @Description: 流式总结消息组件
 */

import * as React from 'react';
import {observer} from 'mobx-react';
import {autorun} from 'mobx';
import {Markdown} from '../../../../../../widgets/gbi-ui';
import {MsgStreamSummary} from '../../../../../../stores/chat/message/msg-stream-summary';
import CopyButton from '../../../../../../widgets/copy-button';
import './index.scss';
import {IMessageCardWidget, IMessageCardWidgetProps, messageCardManager} from '..';

const {useRef, useState, useEffect} = React;
const LoadingPng = require('../../../../../../static/img/gbi-ui/loading.png');

export const StreamSummaryMsg: React.FC<IMessageCardWidgetProps> = observer(
  React.forwardRef(
    (props: IMessageCardWidgetProps, ref) => {
      const contentRef = React.useRef<HTMLDivElement>(null);
      const {qa, message} = props;
      const msg = message as MsgStreamSummary;
      const currentIndexRef = useRef(0); // 当前处理到的索引
      const currentSentenceIndexRef = useRef(0); // 当前sentence中处理到的索引
      const timeoutIdRef = useRef<NodeJS.Timeout | null>(null);
      const [streamText, setStreamText] = useState('');
      const [firstSentenceId, setFirstSentenceId] = useState<number | null>(null);

      const clearTimeoutIdRef = () => {
        if (timeoutIdRef.current) {
          clearTimeout(timeoutIdRef.current);
          timeoutIdRef.current = null;
        }
      };

      // 检查是否是最后一个QA和最后一条消息
      const isLastShowQA = qa.session.qaShowData.at(-1) === qa;
      const isChatLoading = !qa.isQAFinish;

      useEffect(() => {
        let chatDisposer: () => void;
        console.log('🎬 StreamSummaryMsg useEffect 启动, isChatLoading:', isChatLoading);

        if (isChatLoading) {
          chatDisposer = autorun(() => {
            const msgList = msg?.streamData || [];
            const firstId = msg?.streamResult?.[0]?.sentence_id;
            console.log('🔄 autorun 触发:', {
              msgId: msg?.msgId,
              streamDataLength: msgList.length,
              status: msg?.status,
              streamFinished: msg?.streamFinished
            });
            clearTimeoutIdRef();

            // 兼容stream被替换的逻辑，如果firstSentenceId变了，则重置索引，清空组件内streamText
            if (firstId != null) {
              setFirstSentenceId(prev => {
                if (prev !== firstId) {
                  currentIndexRef.current = 0;
                  currentSentenceIndexRef.current = 0;
                  setStreamText('');
                }
                return firstId;
              });
            }

            const streamNextMessage = () => {
              if (currentIndexRef.current < msgList.length) {
                clearTimeoutIdRef();
                const currentSentence = msgList[currentIndexRef.current];
                if (currentSentenceIndexRef.current < currentSentence.length) {
                  let text = msgList.slice(0, currentIndexRef.current).join('');

                  currentSentenceIndexRef.current++;
                  text += currentSentence.slice(0, currentSentenceIndexRef.current);
                  setStreamText(text);
                  timeoutIdRef.current = setTimeout(streamNextMessage, 50);
                } else {
                  currentIndexRef.current++; // 更新当前索引
                  setStreamText(msgList.slice(0, currentIndexRef.current).join(''));
                  currentSentenceIndexRef.current = 0; // 重置当前sentence索引
                  timeoutIdRef.current = setTimeout(streamNextMessage, 100);
                }
              }
            };

            if (msg?.streamFinished) {
              setStreamText(msgList.join(''));
            } else {
              // 如果有未处理的消息，则继续处理
              if (currentIndexRef.current < msgList.length) {
                streamNextMessage();
              }
            }
          });
        } else {
          let text = msg?.streamData?.join('') || '';
          setStreamText(text);
        }

        return () => {
          chatDisposer?.();
          clearTimeoutIdRef();
        };
      }, [msg.streamData]);

      return (
        <div className="stream-summary-msg">
          <div className="stream-summary-msg-content" ref={ref || contentRef}>
            <Markdown
              className="stream-summary-msg-content-markdown"
              content={streamText + (isChatLoading && !msg?.streamFinished ? `![stream-loading](${LoadingPng})` : '')}
            />
          </div>
          {qa.isQAFinish && isLastShowQA && qa.isLastShowMsg(msg) && (
            <CopyButton
              textAlignLeft={true}
              contentNode={
                ((ref as React.RefObject<HTMLDivElement>)?.current || contentRef?.current) as HTMLDivElement
              }
            />
          )}
          {!isLastShowQA && qa.isLastShowMsg(msg) && (
            <div className="stream-summary-msg-content-hover">
              <CopyButton
                isHover={true}
                contentNode={
                  ((ref as React.RefObject<HTMLDivElement>)?.current || contentRef?.current) as HTMLDivElement
                }
              />
            </div>
          )}
        </div>
      );
    }
  )
);

const config: IMessageCardWidget = {
  name: 'stream-summary-message',
  msgType: ['stream_summary'],
  component: StreamSummaryMsg
};

messageCardManager.registerMessageCard(config);
