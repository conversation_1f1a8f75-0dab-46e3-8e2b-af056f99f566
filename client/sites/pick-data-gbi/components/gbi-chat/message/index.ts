/*
 * @Author: den<PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-24 14:49:26
 * @Description:
 */
import {ChartInsight} from './chart';
import {StreamMsg} from './stream';
import {messageCardManager, IMessageCardWidgetProps, IMessageCardWidget} from './messageManager';
import {RecommendQueryMsg} from './recommend-query';
import {TipMessage} from './tip';
import {TextMsg} from './text';
import {MarkDownMsg} from './markdown';
import {ClarifyMsg} from './clarify';
import {DataModelRefsMsg} from './data-model-refs/index';
import {AttributeCard} from './attribute-card';
import {AttributeClarify} from './attribute-clarify';
import './stream-summary';

const configList: IMessageCardWidget[] = [
  {
    name: 'chart-insight-message',
    msgType: ['chart_result_json', 'chart_result_prediction_json'],
    component: ChartInsight
  },
  {
    name: 'stream-message',
    msgType: ['stream_text', 'stream_text_reject', 'stream_text_error'],
    component: StreamMsg
  },
  {
    name: 'recommend-query-message',
    msgType: ['query_recommend_result'],
    component: RecommendQueryMsg
  },
  {
    name: 'insight-tip-msg',
    msgType: ['tip'],
    component: TipMessage
  },
  {
    name: 'text-message',
    msgType: ['text'],
    component: TextMsg
  },
  {
    name: 'markdown-msg',
    msgType: ['data_summary', 'result_text', 'plan_end_error', 'chart_summary'],
    component: MarkDownMsg
  },
  {
    name: 'clarify-message',
    msgType: ['clarify_json'],
    component: ClarifyMsg
  },
  {
    name: 'data-model-refs-message',
    msgType: ['data_model_refs'],
    component: DataModelRefsMsg
  },
  {
    name: 'attribute-message',
    msgType: ['chart_result_reason_json'],
    component: AttributeCard
  },
  {
    // 归因澄清卡片
    name: 'attribute-clarify',
    msgType: ['clarify_reason_index'],
    component: AttributeClarify
  }
];

configList.forEach(config => messageCardManager.registerMessageCard(config));

export {
  RecommendQueryMsg,
  ChartInsight,
  StreamMsg,
  messageCardManager,
  IMessageCardWidgetProps,
  IMessageCardWidget,
  MarkDownMsg,
  ClarifyMsg,
  TipMessage,
  DataModelRefsMsg,
  AttributeCard
};
