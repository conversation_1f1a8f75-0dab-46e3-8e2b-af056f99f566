/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Date: 2024-09-27 14:34:54
 * @Description: 图表卡片底部区域
 */
import classnames from 'classnames';
import sugar_t from '../../../../../utils/i18n';
import * as React from 'react';
import {observer} from 'mobx-react';
import ComTooltip from '../../../../../widgets/com-tooltip';
import {GBIContext} from '../../../../../context';
import {DataInsightChatStore} from '../../../../../stores/data-insight-chat/data-insight-chat-store';
import {MsgChartResult} from '../../../../../stores/data-insight-chat/message';
import {IAssist} from '../../../../../stores';
import {App} from 'antd';
import {pollSummaryContent} from '../../../../../utils/stream-data-helper';
import './index.scss';
const CodePng = require('../../../../../static/img/data-insight/code.svg');
const ExportPng = require('../../../../../static/img/data-insight/export.svg');
const CorePng = require('../../../../../static/img/data-insight/core.svg');

export interface IChartFooter {
  /** 当前消息 */
  message: MsgChartResult;
  /** 展示sql */
  checkSQL: () => void;
  /** 导出 */
  exportData: () => void;
  /** root对象 */
  root?: any;
  assist?: IAssist;
}

export const ChartFooter: React.FC<IChartFooter> = observer(
  React.forwardRef((props: IChartFooter) => {
    const {checkSQL, exportData, message, root, assist} = props;
    const globalContext = React.useContext(GBIContext);
    const store = globalContext.dataInsightChatStore as DataInsightChatStore;
    const {message: messageApi} = App.useApp();
    const [isRegenerating, setIsRegenerating] = React.useState(false);

    const sendMsg = async () => {
      assist?.clientLog?.('reportConsumptionGenerateInsights');
      if (store.currentChartInsight.session.isLoading) {
        return;
      }
      const content = {
        query: '针对上面的图表数据生成洞察结论',
        dataBindType: message.chartEntity.dataType,
        chartData: message.chartData,
        llmJson: message.llmJson,
        dataModelHash: message.chartEntity.dataModelHash
      };
      await store.currentChartInsight.session.sendMessage({
        message: JSON.stringify(content),
        msgFormat: 'chart_summary',
        isUserSend: true
      });
      store.currentChartInsight.session.startQueryMessageListPoll();
    };

    // 重新生成总结
    const regenerateSummary = async () => {
      if (store.currentChartInsight.session.isLoading || isRegenerating) {
        return;
      }

      try {
        setIsRegenerating(true);
        messageApi.loading('正在重新生成总结...', 0);

        // 使用传入的root对象
        if (!root) {
          throw new Error('未找到root对象');
        }

        // 获取当前数据模型 - 需要从message的chartInsight中获取
        const dataModelHash = message.chartInsight?.chart.dataModelHash || '';
        if (!dataModelHash) {
          throw new Error('未找到数据模型Hash');
        }

        // 这里我们需要构造一个简单的dataModel对象，或者修改regenerateSummary方法不需要完整的dataModel
        const mockDataModel = {
          hash: dataModelHash,
          config: {}
        };

        // 调用重新生成总结方法
        const summaryKey = await message.regenerateSummary(
          root,
          store.pageHash,
          mockDataModel as any
        );

        if (summaryKey) {
          // 使用工具函数进行智能轮询
          const summaryContent = await pollSummaryContent(
            (key: string) => message.querySummaryContent(store.pageHash, key),
            summaryKey
          );

          // 总结生成完成，发送消息
          await store.currentChartInsight.session.sendMessage({
            message: summaryContent,
            msgFormat: 'chart_summary',
            isUserSend: false
          });
          store.currentChartInsight.session.startQueryMessageListPoll();
        }
      } catch (error) {
        console.error('重新生成总结失败:', error);
        messageApi.error('重新生成总结失败，请稍后重试');
      } finally {
        setIsRegenerating(false);
        messageApi.destroy();
      }
    };



    // 查看代码
    const handleCheckSQL = () => {
      assist?.clientLog?.('reportConsumptionViewCode');
      checkSQL();
    };

    // 导出数据
    const handleExportData = () => {
      assist?.clientLog?.('reportConsumptionExportData');
      exportData();
    };

    const insightClass = classnames('chart-insight-footer-button', {
      'chart-insight-footer-hide': message?.isReason
    });
    return (
      <div className="chart-insight-footer">
        {!store.currentChartInsight.session.isLoading && (
          <div className={insightClass} onClick={sendMsg}>
            生成洞察
          </div>
        )}
        {!store.currentChartInsight.session.isLoading && !isRegenerating && (
          <div className="chart-insight-footer-button" onClick={regenerateSummary}>
            重新生成总结555
          </div>
        )}
        {isRegenerating && (
          <div className="chart-insight-footer-button chart-insight-footer-loading">
            生成中...
          </div>
        )}
        <ComTooltip placement="top" title={sugar_t('查看代码')}>
          <div onClick={handleCheckSQL} className="chart-insight-footer-iconButton">
            <img src={CodePng} />
            <span>代码</span>
          </div>
        </ComTooltip>
        <ComTooltip placement="top" title={sugar_t('导出数据')}>
          <div onClick={handleExportData} className="chart-insight-footer-iconButton chart-insight-footer-export">
            <img src={ExportPng} />
            <span>导出12122321223</span>
          </div>
        </ComTooltip>
      </div>
    );
  })
);
