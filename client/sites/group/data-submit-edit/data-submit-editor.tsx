/**
 * @file 数据填报编辑页面
 * <AUTHOR>
 */

import sugar_t from '../../../utils/i18n';

import * as React from 'react';
import {Title} from '../../../widgets/document-title';
import {
  inject,
  observer,
  IAssist,
  ICurrentGroup,
  undoManager,
  IEditor,
  IEditorType,
  ICurrentDataSubmit,
  IForm,
  IDataModelTreeList,
  ISqlModel,
  IDataSubmitDatabaseTables
} from '../../../stores/index';
import {<PERSON><PERSON>, <PERSON><PERSON>, toast} from 'amis';
import {confirm} from '../../../widgets/confirm';
import LockAccess from '../../../widgets/editor/lock-access';
import LockHeartBeatAndBeforeunload from '../../../widgets/editor/lockheartbeat-and-beforeunload';
import SubmitContent from '../../../widgets/data-submit-body/data-submit-content';
import SubmitCtrlIndex from '../../../widgets/ctrl-panel/submit-index';
import {getFormVariant} from '../../../widgets/data-submit-forms/form-manager';
import {isSqlName} from '../../../utils/submit-util';
import {SnapshotButtons} from '../../../widgets/snapshot-action';
import {IRouter} from '../../../utils/types';
import AmisRenderer, {EmptyPageRender, getExitDialogTitle} from '../../amis-renderer';
import {defaultCtrlWidth, DocsButton, EditorHeader, EditorResizeContainer} from '../../../widgets/header/editor-header';
import {PickIcon} from '../../../widgets/pick-icon/pick-icon';
import SubmitToolBar from './data-submit-toolbar';
import {getUrlSearchParams} from '../../../utils/gbi-utils/gbi-utils';

const {enableSnapshot} = (window as any).sugarConfig || {};

export interface SubmitEditorProps {
  assist: IAssist;
  group: ICurrentGroup;
  dataSubmit: ICurrentDataSubmit;
  tables: IDataSubmitDatabaseTables;
  editor: IEditor;
  editorType: IEditorType;
  form: IForm;
  sqlModel: ISqlModel;
  dataModelTreeList: IDataModelTreeList;
  routeParams: {
    dataSubmit: string;
  };
  router: IRouter;
}

interface SubmitEditorState {
  openExitDialog: boolean;
}

const checkExistTable = (
  tableSchema: any[],
  fieldName: string,
  allFieldName: string[],
  errorNot: string,
  errorRepeat: string
) => {
  if (!fieldName || !tableSchema.includes(fieldName)) {
    toast.error(errorNot);
    return false;
  } else {
    const index = allFieldName.indexOf(fieldName);
    if (index !== -1) {
      toast.error(errorRepeat);
      return false;
    } else {
      allFieldName.push(fieldName);
    }
  }
  return allFieldName;
};

@inject(sugar => ({
  assist: sugar.assist,
  group: sugar.group.current,
  dataSubmit: sugar.dataSubmit.current,
  tables: sugar.dataSubmit.tables,
  form: sugar.form,
  sqlModel: sugar.sqlModel,
  dataModelTreeList: sugar.dataModel.treeList,
  editor: sugar.editor,
  editorType: 'dataSubmit'
}))
@LockAccess // 编辑锁准入
@LockHeartBeatAndBeforeunload // 编辑锁心跳，以及页面未保存时离开的提示
@observer
export default class DataSubmitEditor extends React.Component<SubmitEditorProps, SubmitEditorState> {
  static displayName = 'DataSubmitEditor';

  constructor(props: SubmitEditorProps) {
    super(props);
    this.undo = this.undo.bind(this);
    this.redo = this.redo.bind(this);
    this.handlePreview = this.handlePreview.bind(this);
    this.getOut = this.getOut.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.justExit = this.justExit.bind(this);
    this.handleClickOnEditorBody = this.handleClickOnEditorBody.bind(this);
    this.onKeyDown = this.onKeyDown.bind(this);
    this.onCopy = this.onCopy.bind(this);
    this.onCut = this.onCut.bind(this);
    this.onPaste = this.onPaste.bind(this);
    this.onCloneForm = this.onCloneForm.bind(this);
    this.removeDocumentEvents = this.removeDocumentEvents.bind(this);
    this.handleCancelExit = this.handleCancelExit.bind(this);
    this.saveThenExit = this.saveThenExit.bind(this);
    this.deleteForms = this.deleteForms.bind(this);
    document.addEventListener('keydown', this.onKeyDown);
    document.addEventListener('copy', this.onCopy);
    document.addEventListener('cut', this.onCut);
    document.addEventListener('paste', this.onPaste);
    const {editor, group, routeParams, editorType, dataModelTreeList} = this.props;
    this.state = {
      openExitDialog: false
    };
    // 初始化编辑器的stores状态
    editor.initialize(group.data.hash, routeParams.dataSubmit, editorType);
    if ((window as any).sugarConfig.enableDataModel) {
      dataModelTreeList.loadData();
    }
  }

  componentWillUnmount() {
    this.removeDocumentEvents();
    this.props.assist.setCurrentPageType('');
  }

  removeDocumentEvents() {
    document.removeEventListener('keydown', this.onKeyDown);
    document.removeEventListener('copy', this.onCopy);
    document.removeEventListener('cut', this.onCut);
    document.removeEventListener('paste', this.onPaste);
  }

  undo() {
    if (undoManager.canUndo) {
      // 如果添加表单后撤销，激活状态就有问题
      this.props.editor.setActiveTarget('dataSubmit');
      undoManager.undo();
    }
  }

  redo() {
    if (undoManager.canRedo) {
      undoManager.redo();
    }
  }

  getOut() {
    const props = this.props;
    if (!props.editor.hasModified) {
      this.justExit();
    } else {
      this.setState({
        openExitDialog: true
      });
    }
  }

  // 表单字段为必填项, 保存的时候需要校验，并校验是否重复
  checkSaveRequired() {
    const allFieldName: string[] = [];
    const formList = this.props.form.list.data;
    const submitEntity = this.props.dataSubmit.data;
    // 如果是已存在的表 (对于已存在的表 可以切换当前使用的表)
    if (submitEntity.isExistTable) {
      const tableSchema = submitEntity.tableSchema.map(item => item.name);

      for (const form of formList) {
        if (!form.config.noFieldName) {
          const result = checkExistTable(
            tableSchema,
            form.fieldName,
            allFieldName,
            sugar_t(`表单项「${form.name}」的表单字段未设置`),
            sugar_t(`表单项「${form.name}」的表单字段与其他表单字段重复`)
          );
          if (!result) return result;
        }
      }
      if (submitEntity.isRealName) {
        const result = checkExistTable(
          tableSchema,
          submitEntity.config.userId,
          allFieldName,
          sugar_t(`用户标识字段未设置`),
          sugar_t('用户标识字段与其他表单字段重复')
        );
        if (!result) return result;
      }
      if (submitEntity.config.submitTime) {
        const result = checkExistTable(
          tableSchema,
          submitEntity.config.submitAt,
          allFieldName,
          sugar_t(`提交时间字段未设置`),
          sugar_t('提交时间字段与其他表单字段重复')
        );
        if (!result) return result;
      }
      if (submitEntity.config.updateTime) {
        const result = checkExistTable(
          tableSchema,
          submitEntity.config.updateAt,
          allFieldName,
          sugar_t(`更新时间字段未设置`),
          sugar_t('更新时间字段与其他表单字段重复')
        );
        if (!result) return result;
      }
    } else {
      // 需要更改表名称则放开
      // const tables = this.props.tables.data;
      // if (tables.includes(submitEntity.tableName)) {
      //   toast.error(sugar_t('数据表名称已存在'));
      //   return false;
      // } else if (!isSqlName.test(submitEntity.tableName)) {
      //   toast.error(sugar_t(`数据表名称格式错误`));
      //   return false;
      // }
      // } else if (submitEntity.tableName.length > 30) {
      //   toast.error(sugar_t(`数据表名称长度超过30`));
      //   return false;
      // }
      for (const form of formList) {
        if (form.name.length > 200) {
          toast.error(sugar_t(`表单项「${form.name}」标题长度超过200`));
          return false;
        }
        if (!form.config.noFieldName) {
          if (!form.fieldName) {
            toast.error(sugar_t(`表单项「${form.name}」的表单字段未设置`));
            return false;
          } else if (!isSqlName.test(form.fieldName)) {
            toast.error(sugar_t(`表单项「${form.name}」的表单字段格式错误`));
            return false;
          } else if (form.fieldName.length > 30) {
            toast.error(sugar_t(`表单项「${form.name}」的表单字段长度超过30`));
            return false;
          } else if (['__id__', '__submit_at__', '__update_at__', '__user_id__'].includes(form.fieldName)) {
            toast.error(sugar_t(`「${form.name}」表单项的「${form.fieldName}」为系统使用字段，请更换名称`));
            return false;
          } else {
            const index = allFieldName.indexOf(form.fieldName);
            if (index !== -1) {
              toast.error(sugar_t(`表单项「${form.name}」的表单字段与「${formList[index].name}」的重复`));
              return false;
            } else {
              allFieldName.push(form.fieldName);
            }
          }
        }
      }
    }
    return true;
  }

  justExit() {
    const props = this.props;
    const searchParam = getUrlSearchParams();
    // 退出前将编辑状态设置为没有未保存的修改，防止 LockHeartBeatAndBeforeunload 修饰器再次提示
    props.editor.setModified(false);
    props.router.push(
      `/group/${props.group.data.key}/manage/dataSubmit?page=${searchParam.page || 1}&parentHash=${
        searchParam.parentHash || ''
      }`
    );
  }

  handleSave() {
    const {editor, group, routeParams} = this.props;
    if ((editor.hasModified || editor.snapshotHash) && editor.savingState !== 'pending' && this.checkSaveRequired()) {
      editor.save(group.data.hash, routeParams.dataSubmit, 'dataSubmit');
    }
  }

  handlePreview() {
    const {editor, group, routeParams} = this.props;
    if ((editor.hasModified || editor.snapshotHash) && editor.savingState !== 'pending') {
      if (this.checkSaveRequired()) {
        editor.save(group.data.hash, routeParams.dataSubmit, 'dataSubmit').then(() => {
          setTimeout(() => {
            window.open(`/group/${group.data.key}/preview/dataSubmit/${routeParams.dataSubmit}?hideReturnButton=1`);
          }, 10);
        });
      }
    } else {
      setTimeout(() => {
        window.open(`/group/${group.data.key}/preview/dataSubmit/${routeParams.dataSubmit}?hideReturnButton=1`);
      }, 10);
    }
  }

  handleClickOnEditorBody() {
    const editor = this.props.editor;
    editor.target.type !== 'dataSubmit' && editor.setActiveTarget('dataSubmit');
  }

  isInTextEditor(e: Event) {
    let selection = window.getSelection();
    if (selection && selection.toString() != '') {
      return true;
    }
    if (
      e.target &&
      (e.target as any).tagName !== 'INPUT' &&
      (e.target as any).tagName !== 'TEXTAREA' &&
      (e.target as any).className !== 'fr-element fr-view'
    ) {
      // 这是富文本编辑器的标示，在富文本编辑器中delete删除不能触发删除表单的操作
      return false;
    } else {
      return true;
    }
  }

  deleteForms() {
    confirm(sugar_t(`确定要删除选中的表单?`))!
      .then(() => {
        this.props.editor.deleteForms();
      })
      .catch(e => {});
  }

  handleCancelExit() {
    this.setState({
      openExitDialog: false
    });
  }

  saveThenExit() {
    const {editor, group, routeParams} = this.props;
    if ((editor.hasModified || editor.snapshotHash) && editor.savingState !== 'pending' && this.checkSaveRequired()) {
      editor.save(group.data.hash, routeParams.dataSubmit, 'dataSubmit').then(e => {
        if (e.state === 'done') {
          this.justExit();
        }
      });
    }
  }

  onKeyDown(e: KeyboardEvent) {
    // 后面的快捷键在文本编辑状态下都需要屏蔽
    if (this.isInTextEditor(e)) {
      return;
    }
    // 删除
    if ((e.keyCode === 8 || e.keyCode === 46) && this.props.editor.target.frontendId) {
      this.deleteForms();
    }
    // ctrl+s / command+s
    if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
      this.handleSave();
      e.preventDefault();
    }
    // ctrl+d / command+d，复制和粘贴
    if ((e.ctrlKey || e.metaKey) && e.keyCode === 68) {
      this.props.editor.copyActiveForm();
      e.preventDefault();
    }
    // ctrl+z / command+z，撤销
    // shift+ctrl+z / shift+command+z，重做
    if ((e.ctrlKey || e.metaKey) && e.keyCode === 90) {
      if (e.shiftKey) {
        this.redo();
      } else {
        this.undo();
      }
    }
  }

  onCloneForm() {
    this.props.editor.copyActiveForm();
  }

  onCopy(e: ClipboardEvent) {
    if (this.isInTextEditor(e)) {
      return;
    }
    this.props.editor.copyToClipboard(e);
  }

  onCut(e: ClipboardEvent) {
    if (this.isInTextEditor(e)) {
      return;
    }
    this.props.editor.cutToClipboard(e);
  }

  onPaste(e: ClipboardEvent) {
    if (this.isInTextEditor(e)) {
      return;
    }
    this.props.editor.clipboardPaste(e);
  }

  render() {
    const {assist, group, dataSubmit, editor, routeParams, form, sqlModel, tables} = this.props;
    if (
      dataSubmit.loadState === 'pending' ||
      (dataSubmit.loadState === 'done' && !dataSubmit.data.hash) ||
      tables.loadState === 'pending'
    ) {
      return <Spinner overlay size="lg" />;
    } else if (dataSubmit.loadState === 'error' || tables.loadState === 'error') {
      return (
        <EmptyPageRender
          emptyText={sugar_t(dataSubmit.loadError.message || '请求错误')}
          link={{to: `/group/${group.data.key}/dataSubmit/${routeParams.dataSubmit}`, label: sugar_t('返回报表空间')}}
        />
      );
    }
    let ctrlTitle = sugar_t('控制面板');
    let docLink: React.ReactNode = (
      <a href="/docs/Submit-Action" target="_blank" className="docs-link">
        {sugar_t('数据填报页面整体配置')}
      </a>
    );
    const activeTargetId = editor.target.frontendId;
    if (editor.target.type === 'form') {
      const activeForm = form.list.data.find(item => item.frontendId === activeTargetId);
      if (activeForm) {
        const rForm = getFormVariant(activeForm.type, activeForm.config.childType);
        if (rForm) {
          ctrlTitle = sugar_t(`控制面板 | 「${activeForm.name}」`);
          let formType = rForm.type;
          if (formType === 'static') {
            formType = 'text/#静态文本';
          }
          const href = `/docs/Submit-Form-${formType}`;
          const docName = rForm.docName || rForm.name;
          docLink = (
            <a href={href} target="_blank" className="docs-link">
              {sugar_t('「' + docName + '」文档')}
            </a>
          );
        }
      }
    }

    return (
      <Title title={`${dataSubmit.data.name}${sugar_t('-编辑')}` || sugar_t('未命名-编辑')}>
        <div className="editor-container-box">
          <EditorHeader
            title={dataSubmit.data.name}
            onBack={this.getOut}
            rightActions={
              <>
                {enableSnapshot ? (
                  <SnapshotButtons
                    groupHash={group.data.hash}
                    resourceType="dataSubmit"
                    resourceHash={dataSubmit.data.hash}
                    editor={editor}
                    useSnapshot={dataSubmit.useSnapshot}
                  />
                ) : null}
                <DocsButton hasFeedback={true} />
                <Button className="wide-btn" disabled={editor.savingState === 'pending'} onClick={this.handlePreview}>
                  {`${!(editor.hasModified || editor.snapshotHash) ? '' : sugar_t('保存并')}` + sugar_t('预览')}
                </Button>
                <Button
                  className="wide-btn"
                  level="primary"
                  disabled={!(editor.hasModified || editor.snapshotHash) || editor.savingState === 'pending'}
                  onClick={this.handleSave}
                >
                  {sugar_t('保存123')}
                </Button>
              </>
            }
            centerActions={<SubmitToolBar groupHash={group.data.hash} addForm={editor.addForm} />}
          />
          <div className="editor-main">
            <div className="editor-drawer d-flex flex-col">
              <div className="editor-drawer-toolbar">
                <div className="text-left">
                  <Button
                    level="link"
                    className="text-initial"
                    iconOnly={true}
                    disabled={!undoManager.canUndo}
                    tooltip={sugar_t('撤销')}
                    tooltipPlacement="bottom"
                    onClick={this.undo}
                  >
                    <PickIcon name="Revocation" />
                  </Button>
                  <Button
                    level="link"
                    className="text-initial"
                    iconOnly={true}
                    disabled={!undoManager.canRedo}
                    tooltip={sugar_t('重做')}
                    tooltipPlacement="bottom"
                    onClick={this.redo}
                  >
                    <PickIcon name="Reform" />
                  </Button>
                  <Button
                    level="link"
                    className="text-initial"
                    iconOnly={true}
                    disabled={!editor.target.frontendId}
                    tooltip={sugar_t('还可以使用键盘 ctrl+c 进行复制，可在别的页面用 ctrl+v 粘贴')}
                    tooltipPlacement="bottom"
                    onClick={this.onCloneForm}
                  >
                    <PickIcon name="Copy" />
                  </Button>
                </div>
                <div className="text-right">
                  <Button level="link" className="text-gray-4 pa-none" onClick={this.handleClickOnEditorBody}>
                    <PickIcon name="Setting" />
                    <span className="m-l-1p">{sugar_t('页面整体配置')}</span>
                  </Button>
                </div>
              </div>
              <div className="flex-1 overflow-hidden">
                <div className="full-box pa-6p overflow-y-auto" onClick={this.handleClickOnEditorBody}>
                  <SubmitContent mode="editor" formStore={form} editor={editor} dataSubmit={dataSubmit} />
                </div>
              </div>
            </div>
            <EditorResizeContainer
              className="editor-ctrl-container"
              title={ctrlTitle}
              toolbar={docLink}
              resizeOption={{
                defaultSize: {
                  width: assist.ctrlPanelWidth || defaultCtrlWidth,
                  height: '100%'
                }
              }}
              defaultFolded={assist.ctrlPanelMin}
              toggleFolded={() => {
                assist.toggleCtrlPanelMin();
              }}
              onResizeStop={(e: any, dir: string, dom: HTMLElement) => {
                assist.setCtrlPanelWidth(dom.offsetWidth);
              }}
            >
              <SubmitCtrlIndex
                dataSubmit={dataSubmit}
                editor={editor}
                formStore={form}
                sqlModelStore={sqlModel}
                tables={tables}
              />
            </EditorResizeContainer>
          </div>
        </div>
        <AmisRenderer
          schema={{
            type: 'dialog',
            show: this.state.openExitDialog,
            onClose: this.handleCancelExit,
            title: getExitDialogTitle(),
            actions: [
              {
                type: 'button',
                disabled: editor.savingState === 'pending',
                onClick: this.handleCancelExit,
                label: sugar_t('取消')
              },
              {
                type: 'button',
                disabled: editor.savingState === 'pending',
                onClick: this.justExit,
                label: sugar_t('直接退出')
              },
              {
                type: 'button',
                primary: true,
                disabled: editor.savingState === 'pending',
                onClick: this.saveThenExit,
                label: sugar_t('保存并退出')
              }
            ],
            body: {
              type: 'tpl',
              tpl: sugar_t('当前还有未保存的内容，直接退出将不会保存你所做的修改。')
            }
          }}
        />
      </Title>
    );
  }
}
