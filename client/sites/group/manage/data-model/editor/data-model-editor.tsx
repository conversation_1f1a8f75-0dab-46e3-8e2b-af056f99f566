/**
 * @file 数据模型编辑
 * <AUTHOR>
 */

import sugar_t from '../../../../../utils/i18n';

import * as React from 'react';
import {Title} from '../../../../../widgets/document-title';
import {Spinner as Lo<PERSON>, <PERSON><PERSON>} from 'amis';
import {
  inject,
  ICurrentGroup,
  IDatabase,
  observer,
  IEditor,
  IEditorType,
  ICurrentDataModel,
  ICurrentDataModelData,
  ICompanyEntity,
  IAssist,
  IUserList,
  IUserAttr,
  IRoleList,
  ICustomTableEntity
} from '../../../../../stores';
import {asyncActionReturn} from '../../../../../stores/helper';
import LockAccess from '../../../../../widgets/editor/lock-access';
import LockHeartBeatAndBeforeunload from '../../../../../widgets/editor/lockheartbeat-and-beforeunload';
import {DataModelEditorTableList} from './table-list';
import DataModelEditorCustomTableList from './custom-table-list';
import DataModelEditorTableJoin from './table-join';
import DataModelEditorBody from './data-model-body';
import '../../../report-edit/editor.scss';
import './data-model-editor.scss';
import {SnapshotButtons} from '../../../../../widgets/snapshot-action';
import {IRouter} from '../../../../../utils/types';
import AmisRenderer, {EmptyPageRender, getExitDialogTitle} from '../../../../amis-renderer';
import {TourGuide} from './tour-guide';
import EllipsisTooltip from '../../../../../widgets/ellipsis-tooltip/ellipsis-tooltip';
import {DocsButton, EditorHeader} from '../../../../../widgets/header/editor-header';

export interface DataModelEditorProps {
  assist: IAssist;
  database: IDatabase;
  company: ICompanyEntity;
  group: ICurrentGroup;
  dataModel: ICurrentDataModel;
  dataModelData: ICurrentDataModelData;
  customTable: ICustomTableEntity;
  editor: IEditor;
  editorType: IEditorType;
  routeParams: {
    dataModel: string;
  };
  userList: IUserList;
  userAttr: IUserAttr;
  roleList: IRoleList;
  router: IRouter;
}

interface DataModelEditorState {
  editModelName: boolean;
  openExitDialog: boolean;
  leftWidth: number;
  topHeight: number;
  topHeightTableList: number;
  topHeightCustomTableList: number;
  syncStructure: boolean;
}

const {enableSnapshot} = (window as any).sugarConfig || {};

const initSliderHeight =
  document.body.clientHeight -
  58 /*顶部栏 */ -
  21 /*数据源 */ -
  68 /*数据源列表 */ -
  (21 + 34 + 13 + 20) * 2 /*名称、搜索框、padding */ -
  12; /*滑动块 */

const {disableBI} = (window as any).sugarConfig || {};

@inject(sugar => ({
  assist: sugar.assist,
  database: sugar.database,
  company: sugar.company.info,
  group: sugar.group.current,
  dataModel: sugar.dataModel.current,
  dataModelData: sugar.dataModel.currentData,
  editor: sugar.editor,
  editorType: 'dataModel',
  userList: sugar.user.all,
  userAttr: sugar.userAttr,
  roleList: sugar.role.all,
  customTable: sugar.customTable
}))
@LockAccess // 编辑锁准入
@LockHeartBeatAndBeforeunload // 编辑锁心跳，以及页面未保存时离开的提示
@observer
export default class DataModelEditor extends React.Component<DataModelEditorProps, DataModelEditorState> {
  static displayName = 'DataModelEditor';

  sliderHeight = initSliderHeight;
  resizing = false;
  startX = 0;
  startWidth = 0;
  startY = 0;
  startYTableList = 0;
  startHeight = 0;
  startHeightTableList = 0;
  leftWidth = 0;
  topHeight = 0;
  topHeightTableList = this.sliderHeight * 0.7;

  constructor(props: DataModelEditorProps) {
    super(props);

    this.toggleModelNameEdit = this.toggleModelNameEdit.bind(this);
    this.changeModelName = this.changeModelName.bind(this);
    this.handleMouseDownLeft = this.handleMouseDownLeft.bind(this);
    this.handleMouseMoveLeft = this.handleMouseMoveLeft.bind(this);
    this.handleMouseUpLeft = this.handleMouseUpLeft.bind(this);
    this.handleMouseDownTop = this.handleMouseDownTop.bind(this);
    this.handleMouseMoveTop = this.handleMouseMoveTop.bind(this);
    this.handleMouseMoveTopTableList = this.handleMouseMoveTopTableList.bind(this);
    this.handleMouseUpTop = this.handleMouseUpTop.bind(this);
    this.removeWindowEvents = this.removeWindowEvents.bind(this);
    this.getOut = this.getOut.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.handleCancelExit = this.handleCancelExit.bind(this);
    this.justExit = this.justExit.bind(this);
    this.saveThenExit = this.saveThenExit.bind(this);

    const {assist, dataModel, editor, editorType, group, routeParams, database} = this.props;
    this.leftWidth = assist.modelEditorLeftWidth;
    if (this.leftWidth < 230) {
      this.leftWidth = 230;
    }
    this.topHeight = Math.max(assist.modelEditorTopHeight, 230);
    let dataSourceHeight = 68;
    const dmSchema = dataModel.data?.config;
    if (dmSchema.dbHashes.length > 1 || dmSchema.homologous.length > 1) {
      dataSourceHeight += 58; // margin 重合
      this.sliderHeight = initSliderHeight - 58;
    } else {
      if (dmSchema.homologous.length > 0) {
        dataSourceHeight += 40.5; // margin 重合
        this.sliderHeight = initSliderHeight - 40.5;
      }
    }
    this.topHeightTableList = this.sliderHeight * 0.7;

    this.state = {
      leftWidth: this.leftWidth,
      topHeight: this.topHeight,
      topHeightTableList: this.topHeightTableList,
      topHeightCustomTableList: this.topHeightTableList + 21 + dataSourceHeight + 12 + 68 + 20,
      openExitDialog: false,
      editModelName: false,
      syncStructure: false
    };

    // 初始化编辑器的stores状态
    (editor.initialize(group.data.hash, routeParams.dataModel, editorType) as asyncActionReturn).then(ret => {
      if (ret.state === 'done') {
        // 拉取数据模型的第一个数据源信息（主数据源）
        database.current.load(group.data.hash, ret.res?.data.config.dbHashes[0]);
      }
    });
    // 拉取空间中所有数据源的名称和hash列表
    if (!database.simpleList.data || !database.simpleList.data.length) {
      database.simpleList.loadData();
    }
  }

  UNSAFE_componentWillReceiveProps(nextProps: DataModelEditorProps) {
    const dmSchema = nextProps.dataModel.data?.config;
    this.sliderHeight =
      dmSchema.dbHashes.length > 1 || dmSchema.homologous.length > 1
        ? initSliderHeight - 58
        : dmSchema.homologous.length > 0
        ? initSliderHeight - 40.5
        : initSliderHeight;

    this.topHeightTableList = this.sliderHeight * 0.7;
  }

  componentWillUnmount() {
    this.props.database.current.destroy();
    this.removeWindowEvents();
  }

  toggleModelNameEdit() {
    this.setState(
      {
        editModelName: !this.state.editModelName
      },
      () => {
        if (this.state.editModelName === true) {
          // 模型名称切换到编辑状态时，自动全选
          const ele = document.getElementById('edit-model-name');
          if (ele && (ele as any).select) {
            (ele as any).select();
          }
        }
      }
    );
  }

  changeModelName(e: any) {
    this.props.dataModel.data?.rename(e.target.value || '');
  }

  handleMouseDownLeft(e: any) {
    this.startX = e.clientX;
    this.startWidth = this.state.leftWidth;
    this.resizing = true;
    window.addEventListener('mouseup', this.handleMouseUpLeft);
    window.addEventListener('mousemove', this.handleMouseMoveLeft);
    e.stopPropagation && e.stopPropagation();
  }

  handleMouseMoveLeft(e: MouseEvent) {
    const diff = this.startX - e.clientX;
    e.preventDefault();
    this.leftWidth = Math.min(350, Math.max(230, this.startWidth - diff));
    this.setState({
      leftWidth: this.leftWidth
    });
  }

  handleMouseUpLeft() {
    this.resizing = false;
    this.removeWindowEvents();
    this.props.assist.setModelEditorLeftWidth(this.leftWidth);
  }

  handleMouseDownTop(e: any, type: string) {
    if (type === 'tableJoin') {
      this.startY = e.clientY;
      this.startHeight = this.state.topHeight;
    } else {
      this.startYTableList = e.clientY;
      this.startHeightTableList = this.state.topHeightTableList;
    }

    this.resizing = true;
    if (type === 'tableJoin') {
      window.addEventListener('mouseup', this.handleMouseUpTop);
      window.addEventListener('mousemove', this.handleMouseMoveTop);
    } else {
      window.addEventListener('mouseup', this.removeWindowEvents);
      window.addEventListener('mousemove', this.handleMouseMoveTopTableList);
    }

    e.stopPropagation && e.stopPropagation();
  }

  handleMouseMoveTopTableList(e: MouseEvent) {
    const diff = this.startYTableList - e.clientY;
    e.preventDefault();
    const dmSchema = this.props.dataModel.data?.config;
    const offset =
      dmSchema.dbHashes.length > 1 || dmSchema.homologous.length > 1 ? 79 : dmSchema.homologous.length > 0 ? 61.5 : 21;
    this.topHeightTableList = Math.min(this.sliderHeight - offset, Math.max(180, this.startHeightTableList - diff));
    this.setState({
      topHeightTableList: this.topHeightTableList,
      topHeightCustomTableList: document.getElementsByClassName('wrapper-sm table-list')[0]?.clientHeight + 12
    });
  }

  handleMouseMoveTop(e: MouseEvent) {
    const diff = this.startY - e.clientY;
    e.preventDefault();
    this.topHeight = Math.min(350, Math.max(230, this.startHeight - diff));
    this.setState({
      topHeight: this.topHeight
    });
  }

  handleMouseUpTop() {
    this.resizing = false;
    this.removeWindowEvents();
    this.props.assist.setModelEditorTopHeight(this.topHeight);
  }

  removeWindowEvents() {
    window.removeEventListener('mouseup', this.handleMouseUpLeft);
    window.removeEventListener('mousemove', this.handleMouseMoveLeft);
    window.removeEventListener('mouseup', this.handleMouseUpTop);
    window.removeEventListener('mousemove', this.handleMouseMoveTop);
    window.removeEventListener('mousemove', this.handleMouseMoveTopTableList);
  }

  // 退出按钮点击
  getOut() {
    if (this.props.editor.savingState === 'pending') {
      return;
    }
    const {editor} = this.props;
    if (!(editor.hasModified || editor.snapshotHash)) {
      this.justExit();
    } else {
      this.setState({
        openExitDialog: true
      });
    }
  }

  handleSave() {
    const {editor, group, routeParams} = this.props;
    if ((editor.hasModified || editor.snapshotHash) && editor.savingState !== 'pending') {
      editor.save(group.data.hash, routeParams.dataModel, 'dataModel');
      console.log('savv');
    }
  }

  handleCancelExit() {
    this.setState({
      openExitDialog: false
    });
  }

  justExit() {
    const {editor, router, group} = this.props;
    // 退出前将编辑状态设置为没有未保存的修改，防止 LockHeartBeatAndBeforeunload 修饰器再次提示
    editor.setModified(false);
    if ((window as any).comeFromModelManage) {
      // 退出到上一地址，模型管理页面有文件目录
      (window as any).comeFromModelManage = null;
      router.goBack();
    } else {
      router.push(`/group/${group.data.key}/manage/dataModel`);
    }
  }

  saveThenExit() {
    const {editor, group, routeParams} = this.props;
    if ((editor.hasModified || editor.snapshotHash) && editor.savingState !== 'pending') {
      editor.save(group.data.hash, routeParams.dataModel, 'dataModel').then(e => {
        if (e.state === 'done') {
          this.justExit();
        }
      });
    }
  }

  render() {
    const {company, group, database, dataModel, dataModelData, editor, userAttr, userList, roleList, customTable} =
      this.props;

    const {leftWidth, topHeight, topHeightTableList, openExitDialog, syncStructure, topHeightCustomTableList} =
      this.state;

    // 拉取当前数据模型的具体信息 同步表结构时造成的数据刷新不整体刷新页面
    if (
      !syncStructure &&
      (dataModel.loadState === 'pending' || (dataModel.loadState === 'done' && !dataModel.data?.hash))
    ) {
      return <Loading overlay size="lg" />;
    } else if (dataModel.loadState === 'error') {
      return (
        <EmptyPageRender
          emptyText={sugar_t(dataModel.loadError.message || '请求错误')}
          link={{to: `/group/${group.data.key}/manage/dataModel`, label: sugar_t('返回空间')}}
        />
      );
    }

    // 拉取数据源简单列表数据
    if (
      database.simpleList.loadState === 'pending' ||
      (database.simpleList.loadState === 'done' && !database.simpleList.data)
    ) {
      return <Loading overlay size="lg" />;
    } else if (database.simpleList.loadState === 'error') {
      return (
        <EmptyPageRender
          emptyText={sugar_t(database.simpleList.loadError.message || '请求错误')}
          link={{to: `/group/${group.data.key}/manage/dataModel`, label: sugar_t('返回空间')}}
        />
      );
    }

    // 拉取数据模型的第一个数据源具体信息（主数据源）
    if (
      database.current.loadState === 'pending' ||
      (database.current.loadState === 'done' && !database.current.data.hash)
    ) {
      return <Loading overlay size="lg" />;
    } else if (database.current.loadState === 'error') {
      return (
        <EmptyPageRender
          emptyText={sugar_t(database.current.loadError.message || '请求错误')}
          link={{to: `/group/${group.data.key}/manage/dataModel`, label: sugar_t('返回空间')}}
        />
      );
    }

    // 如果本数据模型中使用的所有数据源，在数据源简单列表中没有找到，证明是当前用户没有对应数据源的使用权限，直接提示，否则本页面中后续逻辑中会有问题
    const hasNoAuth = dataModel.data?.config?.dbHashes?.some(
      hash => !(database.simpleList.data as PlainObject[]).find(item => hash === item.hash)
    );
    if (hasNoAuth) {
      return (
        <EmptyPageRender
          emptyText={sugar_t('该数据模型所使用的数据源，您没有使用权限，需要空间管理员给您赋权')}
          link={{to: `/group/${group.data.key}/manage/dataModel`, label: sugar_t('返回空间')}}
        />
      );
    }

    return (
      <Title title={sugar_t((dataModel.data?.name || '未命名') + '--数据模型编辑')}>
        <div className="editor-container data-model-editor">
          <EditorHeader
            title={dataModel.data.name || '未命名'}
            onBack={this.getOut}
            dataModel={dataModel}
            showEdit={true}
            deviceSwitch={false}
            orientSwitch={false}
            rightActions={
              <>
                {enableSnapshot ? (
                  <SnapshotButtons
                    resourceType="dataModel"
                    resourceHash={dataModel.data.hash}
                    groupHash={group.data.hash}
                    editor={editor}
                    useSnapshot={dataModel.useSnapshot}
                  />
                ) : null}
                <DocsButton hasFeedback={true} dropdownAlign="right" />
                <Button
                  className="wide-btn"
                  level="primary"
                  disabled={!(editor.hasModified || editor.snapshotHash) || editor.savingState === 'pending'}
                  onClick={this.handleSave}
                >
                  {sugar_t('保存bbq')}
                </Button>
              </>
            }
          />

          {/* 左侧数据表列表 */}
          <div
            ref="editorCtrlPanel"
            className="editor-ctrl-panel data-model-editor__table-list"
            style={{width: leftWidth}}
          >
            <div className="editor-ctrl-panel__body">
              <DataModelEditorTableList
                database={database}
                dataModel={dataModel}
                editor={editor}
                topHeightTableList={disableBI ? this.sliderHeight : topHeightTableList}
                group={group}
                hasCustomTable={customTable.list.loadState !== 'done' ? true : !!customTable.list.data.length}
              />

              {!disableBI && (
                <>
                  <div className="resizer-h" onMouseDown={e => this.handleMouseDownTop(e, 'tableList')}>
                    <i className="fa fa-bars" />
                    <i className="fa fa-bars" />
                  </div>
                  <DataModelEditorCustomTableList
                    database={database}
                    dataModel={dataModel}
                    customTable={customTable}
                    topHeightCustomTableList={topHeightCustomTableList}
                    group={group}
                  />
                </>
              )}
            </div>
            <div className="resizer" onMouseDown={this.handleMouseDownLeft} />
          </div>

          {/* 顶部数据表join区域 */}
          <div className="data-model-editor__table-join" style={{left: leftWidth, height: topHeight}}>
            <DataModelEditorTableJoin
              dmSchema={dataModel.data?.config}
              editor={editor}
              database={database}
              customTable={customTable}
            />
            <div className="resizer-h" onMouseDown={e => this.handleMouseDownTop(e, 'tableJoin')}>
              <i className="fa fa-bars" />
              <i className="fa fa-bars" />
            </div>
          </div>

          {/* 数据字段建模和数据预览 */}
          <div
            id="data-model-editor-guide-3"
            className="data-model-editor__body"
            style={{left: leftWidth, top: topHeight + 60}}
          >
            <DataModelEditorBody
              dataModel={dataModel}
              dataModelEntity={dataModel.data}
              dmSchema={dataModel.data?.config}
              dataModelHash={dataModel.data?.hash}
              dataModelData={dataModelData}
              editor={editor}
              group={group}
              userList={userList}
              userAttr={userAttr}
              roleList={roleList}
              syncStructure={() => this.setState({syncStructure: true})}
            />
          </div>
          <AmisRenderer
            schema={{
              type: 'dialog',
              show: this.state.openExitDialog,
              onClose: this.handleCancelExit,
              title: getExitDialogTitle(),
              actions: [
                {
                  type: 'button',
                  disabled: editor.savingState === 'pending',
                  onClick: this.handleCancelExit,
                  label: sugar_t('取消')
                },
                {
                  type: 'button',
                  disabled: editor.savingState === 'pending',
                  onClick: this.justExit,
                  label: sugar_t('直接退出')
                },
                {
                  type: 'button',
                  primary: true,
                  disabled: editor.savingState === 'pending',
                  onClick: this.saveThenExit,
                  label: sugar_t('保存并退出')
                }
              ],
              body: {
                type: 'tpl',
                tpl: sugar_t('当前还有未保存的内容，直接退出将不会保存你所做的修改。')
              }
            }}
          />
        </div>
        <TourGuide />
      </Title>
    );
  }
}
