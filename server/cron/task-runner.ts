/**
 * @file 定期任务的基类，需要被继承来使用
 * <AUTHOR>
 *
 * 注意：当前为测试模式，部分任务跳过Redis分布式锁
 * 原因：Redis连接问题（connected: false, ready: false）
 * 修复Redis连接后，请删除无锁模式的临时代码
 */

import Redlock = require('redlock');
import {sequelize, Task, ITaskInstance} from '../models/index';
import redis = require('redis');
import {redisClient} from '../lib/redis';

let redlock = new Redlock([redisClient as redis.RedisClient], {
  // the expected clock drift; for more details
  // see http://redis.io/topics/distlock
  driftFactor: 0.01, // time in ms
  retryCount: 1,
  retryDelay: 50, // time in ms
  retryJitter: 10 // time in ms
});

redlock.on('clientError', function (err) {
  yog.log.warning('task_runner_redis_redlock_client_error :' + JSON.stringify(err));
});

export abstract class TaskRunner {
  type: string;
  ttl = 30 * 60 * 1000;
  retryTTL = 10 * 60 * 1000;
  enabled: boolean;
  canRetry = true;
  timeId: NodeJS.Timeout;
  constructor(type: string, ttl?: number) {
    this.enabled = true;
    this.type = type;
    if (ttl) {
      this.ttl = ttl;
    }
    this.init();
  }
  async init() {
    // 每6到12秒（随机）执行一次 getTask
    this.timeId = setInterval(this.getTask.bind(this), (Math.ceil(Math.random() * 6) + 6) * 1000);
  }
  destroy() {
    this.enabled = false;
  }
  async getTask() {
    // reload 的时候干掉了
    if (!this.enabled) {
      if (this.timeId) {
        clearInterval(this.timeId);
      }
      return;
    }
    let now = new Date();
    // 执行时间太长的请求设置为超时
    let expireTime = new Date();
    expireTime.setTime(now.getTime() - this.ttl);
    await Task.update(
      {
        status: -2
      },
      {
        where: {
          type: this.type,
          run_at: {
            $lte: expireTime
          },
          status: 1
        }
      }
    );

    // 对于可以重试的任务进行重置，十分钟内
    if (this.canRetry) {
      let retryTime = new Date();
      retryTime.setTime(now.getTime() - this.retryTTL);
      await Task.update(
        {
          status: 0
        },
        {
          where: {
            type: this.type,
            run_at: {
              $gte: retryTime
            },
            status: -1
          }
        }
      );
    }

    // 判断一下最近1分钟内启动的，并且还处于运行中的某类型任务数量，如果超过20个，则不在启动该类型的新任务了，防止对宿主机造成较大的压力
    // 这块主要是内网Sugar，早上10点以及整点、半点的时候，会启动大量的任务，导致机器CPU/内存压力过大，导致机器挂掉
    // 当前只判断 定时邮件/数据预警/页面截缩略图 这几类任务
    if (['mail-page', 'chart-warning', 'thumb-image'].includes(this.type)) {
      const count = await Task.count({
        where: {
          status: 1,
          type: this.type,
          updated_at: {
            $gte: new Date(+new Date() - 60 * 1000)
          }
        }
      });
      if (count > parseInt(process.env.sugar_task_max_concurrent_count || '20', 10)) {
        return;
      }
    }

    // 取一个待执行的任务来执行
    let task = await Task.findOne({
      where: {
        type: this.type,
        run_at: {
          $lte: now
        },
        status: 0
      },
      order: sequelize.random()
    });

    // 添加调试日志
    if (this.type === 'dataModel-update') {
      const allTasks = await Task.findAll({
        where: {
          type: this.type
        },
        order: [['created_at', 'DESC']],
        limit: 5
      });
      console.log('dataModel-update任务查询结果:', {
        current_time: now,
        found_task: task ? task.id : null,
        all_recent_tasks: allTasks.map(t => ({
          id: t.id,
          status: t.status,
          run_at: t.run_at,
          created_at: t.created_at,
          should_run: t.run_at <= now && t.status === 0
        }))
      });
    }

    if (task) {
      let taskId = task.id;
      try {
        let lock;

        // ========== 临时无锁模式 ==========
        // 由于Redis连接问题（connected: false, ready: false），
        // 临时跳过分布式锁获取，仅用于测试功能。
        //
        // 注意：生产环境中应该修复Redis连接问题并启用分布式锁，
        // 以防止同一任务被多个进程同时执行。
        //
        // 修复Redis后，删除此if条件即可恢复正常的锁机制。
        // =====================================
        if (['dataModel-update', 'sys-auto-learn'].includes(this.type)) {
          console.log(`[TaskRunner-${this.type}] 🚫 临时跳过Redis锁获取（测试模式 - Redis未连接）`);
          lock = null;
        } else {
          try {
            lock = await redlock.lock(`task:${task.id}`, 50000);
            console.log(`[TaskRunner-${this.type}] ✅ 分布式锁获取成功`);
          } catch (lockError) {
            console.error(`[TaskRunner-${this.type}] ❌ 分布式锁获取失败: ${lockError.message}`);
            yog.log.warning(`[task-runner] Redis lock failed for task ${task.id}: ${lockError.message}`);
            lock = null;
          }
        }

        // 再查询一下状态是否为 0，避免重复执行
        let currentTask = await Task.findOne({
          where: {
            id: task.id,
            status: 0
          }
        });
        if (currentTask) {
          try {
            task.status = 1;
            await task.save();
            await this.doWork(task);
            //'mail-page', 'chart-warning'在重试机制中统一控制状态
            if (!['mail-page', 'chart-warning'].includes(this.type)) {
              task.status = 2;
              await task.save();
            }
          } catch (e) {
            yog.log.warning(`[task-runner] run ${task.type} task error id: ${taskId}, error: ${e.message}`);
            task.status = -1;
            // 错误信息写到config中的_errorMsg_字段, 错误详细信息写到config中的_errorStack_字段
            let config = JSON.parse(task.config || '{}');
            config._errorMsg_ = e.message;
            if (e.stack) {
              config._errorStack_ = e.stack;
            }
            task.config = JSON.stringify(config);
            await task.save();
          }
        }

        // 释放锁（如果获取到了锁）
        if (lock && typeof (lock as any).unlock === 'function') {
          try {
            await (lock as any).unlock();
            console.log(`[TaskRunner-${this.type}] 🔓 分布式锁已释放`);
          } catch (unlockError) {
            console.error(`[TaskRunner-${this.type}] ⚠️ 分布式锁释放失败: ${unlockError.message}`);
          }
        }
      } catch (e) {
        // 未获得锁，不处理
        console.log(`[TaskRunner-${this.type}] ⚠️ 任务执行异常: ${e.message}`);
      }
    }
  }
  /**
   * 具体实现的类型需要覆盖这个方法
   * @param task 任务实例
   */
  abstract doWork(task: ITaskInstance): Promise<void>;
}
