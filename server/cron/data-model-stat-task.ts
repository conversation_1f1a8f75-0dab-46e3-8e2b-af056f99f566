/**
 * @file 进行数据统计 / 发送数据到 unit 的任务
 *       任务直接插入 task 表，不依赖 scheduler
 *       目前在数据模型保存时会插入一个 5 分钟后执行的任务
 * <AUTHOR>
 */
import {
  ITaskInstance,
  Company,
  ICompanyInstance,
  DataModel,
  Task,
  IDataModelInstance,
  Group,
  IGroupInstance,
  KV
} from '../models/index';
import {TaskRunner} from './task-runner';
import {DataModelSchema} from '../data-adapter/data-model-proxy/dialect/base/data-model-schema';
// import {getDbQueryBuilder} from '../data-adapter/data-model-proxy/db-query-builder';
import {getCustomTablesByHash} from '../service/custom-table';
// import {getDbDriver} from '../data-adapter/data-model-proxy/db-driver';
import {loadProductConfig} from '../middlewares/saas-company';
import {getConfigOfDataModel, getDataModelByWhere, updateDataModel} from '../service/data-model';
import {createNLPSkill, createOrUpdateNLPModel, getSkillId} from '../service/nlp';
import {NLP_NAME} from '../action/data-model';
import {DataModelParams, dataModelProxy, DataModelProxyReturn} from '../data-adapter/data-model-proxy/data-model-proxy';
import {CommonError} from '../lib/errors';
import {setKV} from '../service/kv';
import {updateDMLearnTask} from '../service/data-model-learn';

const taskType = 'dataModel-update';
const {deployType, gbi} = yog.conf.sugar;

export class DataModelStatTask extends TaskRunner {
  constructor() {
    super(taskType);
  }
  async doWork(task: ITaskInstance) {
    console.log('data-upate-dowork--------------------')
    console.log('执行任务详情:', {
      id: task.id,
      type: task.type,
      status: task.status,
      run_at: task.run_at,
      current_time: new Date(),
      schedule_id: task.schedule_id,
      config: task.config
    });
    let dataModelId = task.schedule_id;
    let originalDataModel = await DataModel.findOne({
      where: {
        id: dataModelId
      }
    });
    let dataModel = await getDataModelByWhere({id: dataModelId});

    let config = JSON.parse(task.config);
    let companyId = config.companyId;
    let company = await Company.findOne({where: {id: companyId}});
    if (!dataModel || !company || !originalDataModel) {
      await Task.destroy({
        where: {
          schedule_id: dataModelId,
          type: taskType
        },
        force: true
      });
      return;
    }

    // 标记模型训练中
    originalDataModel.nlp_state = 1;
    await originalDataModel.save();
    console.log('originalDataModel', originalDataModel);

    try {
      // 拉取模型维度字段们
      let res = await getDimDistinctData(dataModel, company);
      let resForDateTime = await getDimDateBeginData(dataModel, company);
      let group = await Group.findOne({
        where: {
          id: task.group_id
        }
      });

      if (!group) {
        throw new Error('空间不存在');
      }

      let dataModelSchema: DataModelSchema = dataModel.config as any;
      console.log('dataModelSchema', dataModelSchema);

      if (config.smartAnswer && originalDataModel.nlp_open) {
        // 是否需要创建技能
        let skillId = getSkillId(company, group);

        if (!skillId) {
          if (deployType === 'saas') {
            skillId = await createNLPSkill(company.email_suffix, company.name);
            company.config = {
              ...(company.config as any),
              skillId
            };
            await company.save();
          } else if (deployType === 'private') {
            let groups = await Group.findAll({
              where: {
                company_id: company.id
              }
            });
            let num = 0;
            for (let group of groups) {
              if (group.config.skillId) {
                num++;
              }
            }
            if (num >= yog.conf.sugar.nlp.privateMaxSkillNum) {
              throw new Error(
                `目前仅支持在「${yog.conf.sugar.nlp.privateMaxSkillNum}」个空间中开启数据模型的「${NLP_NAME}」功能，每个空间中可放置多个开启「${NLP_NAME}」功能的数据模型`
              );
            }
            if (group) {
              skillId = await createNLPSkill(group.hash, group.name);
              group.config = {
                ...group.config,
                skillId
              };
              await group.save();
            } else {
              throw new Error(`要创建 NLP Skill 的空间不存在`);
            }
          }
        }
        // 开始创建/更新 NLP 模型
        await createOrUpdateNLPModel(skillId, dataModel.hash, dataModel.name, dataModelSchema, res);
      }
      let newDataModel = await DataModel.findOne({where: {id: dataModelId}});
      console.log('newDataModel');
      if (newDataModel) {
        // 上面的dataModel必须那样findOne，不能用service中的getDataModelByHash来获取，因为下面的updateDataModel方法中要用到，不能替换dataModel中的config为真正的JSON格式的config
        // 因此下面一行 getConfigOfDataModel 方法的第二个参数也必须为 true，不能更改dataModel中的config字段
        dataModelSchema = await getConfigOfDataModel(newDataModel, true);
        if (config.statistics) {
          await statisticsDataModel(dataModelSchema, res, resForDateTime, group);
        }
        newDataModel.nlp_state = 2;
        await updateDataModel(newDataModel, newDataModel.name, dataModelSchema, undefined, undefined, true, true);
        if (gbi?.enableAIAssistant && gbi.ESDataIndexURL) {
          console.log('数据模型发生改动，有问数配置的学习任务需要重新开始学习')
          // 数据模型发生改动，有问数配置的学习任务需要重新开始学习
          await updateDMLearnTask({group: group, dataModel: dataModel});
        }
      }
    } catch (e) {
      let newOriginalDataModel = await DataModel.findOne({
        where: {
          id: dataModelId
        }
      });
      if (newOriginalDataModel) {
        newOriginalDataModel.nlp_state = -1;
        await newOriginalDataModel.save();
      }
      console.log('error', e);
      yog.log.warning(`[dataModel nlp task] ${dataModel.hash} error ：` + e);
    }
  }
}

export interface FieldsData {
  [propsName: string]: any[];
}

/**
 * 获取某一个数据模型中每个string类型维度的去重取值（只取了最多1000条）。返回的数据是一个使用维度的id做key的对象
 * 对于text类型只取前100条，字符串截取前100个字符，防止数据量过大，数据模型学习失败
 * @param dataModel 数据模型
 * @param company 当前组织
 */
export async function getDimDistinctData(
  dataModel: IDataModelInstance,
  company: ICompanyInstance,
  group?: IGroupInstance,
  limit?: number
) {
  const dataModelSchema: DataModelSchema = dataModel.config as any;
  if (!dataModelSchema.tables || !dataModelSchema.tables.length) {
    return {};
  }
  const dimsMap = dataModelSchema.dimensions;
  const selectDimensions: ISelectDimension[] = [];
  // 查询所有的维度，包括了计算维度，剔除预测维度
  for (const menu of dataModelSchema.dimensionMenu) {
    if (menu.predictType) {
      continue;
    }
    menu.nodes.forEach(nodeId => {
      const dim = dimsMap[nodeId];
      if (dim) {
        if (
          dim.type === 'dimension' &&
          !dim.isHidden &&
          // 兼容下分桶，分桶的 dataType 是 int
          (dim.dataType === 'string' || dim.dataType === 'int') &&
          (dim.convert.type === '' || dim.convert.type === 'string')
        ) {
          selectDimensions.push({
            id: nodeId,
            frontendId: nodeId,
            type: 'd'
          });
        } else if (dim.type === 'hierarchy' && dim.pathIds && dim.pathIds.length) {
          dim.pathIds.forEach(subNodeId => {
            const subDim = dimsMap[subNodeId];
            if (
              subDim.type === 'dimension' &&
              !subDim.isHidden &&
              // 兼容下分桶，分桶的 dataType 是 int
              (subDim.dataType === 'string' || subDim.dataType === 'int') &&
              (subDim.convert.type === '' || subDim.convert.type === 'string')
            ) {
              selectDimensions.push({
                id: subNodeId,
                frontendId: subNodeId,
                type: 'd'
              });
            }
          });
        }
      }
    });
  }

  let res: FieldsData = {};

  // let sql = '';
  // const customTable = await getCustomTablesByHash(dataModel.hash);
  // const {databases, queryBuilder} = await getDbQueryBuilder(dataModelSchema, dataModel.group_id);
  // const database = databases[0];
  // let dummyReq: any = {
  //   company,
  //   productConfig: {}
  // };
  // if (yog.conf.sugar.deployType === 'saas') {
  //   loadProductConfig(dummyReq);
  // }
  // for (let dim of selectDimensions) {
  //   let resArr: any[] = [];
  //   sql = queryBuilder.getSQL({
  //     selectDimensions: [dim],
  //     customTable,
  //     dataModelSchema,
  //     notAggregate: true,
  //     needDistinct: true,
  //     limit: 1000
  //   });
  //   const driver = await getDbDriver(dummyReq, {
  //     type: database.type,
  //     database_name: database.database_name,
  //     tunnel_id: database.tunnel_id,
  //     host: database.host,
  //     port: database.port,
  //     username: database.username,
  //     password: database.password,
  //     config: database.config
  //   });
  //   let raw = await driver.runQuery(sql, true);
  //   for (let row of raw) {
  //     resArr.push(row[dim.id]);
  //   }
  //   res[dim.id] = resArr;
  // }
  // 将上面注释掉的代码改成了以下的方式来实现，因为要支持跨数据源的数据模型，所有的逻辑都统一在 data-model-proxy.ts 中来实现
  const customTable = await getCustomTablesByHash(dataModel.hash);
  let dummyReq = {
    company,
    productConfig: {} as any
  };
  if (yog.conf.sugar.deployType === 'saas') {
    loadProductConfig(dummyReq);
  }
  if (!group) {
    group = (await dataModel.getGroup()) as IGroupInstance;
  }
  const fakeRequest: IDataModelProxyFakeRequest = {
    ...dummyReq,
    ignoreDataLineLimits: true, // 忽略行级别权限的限制
    ignoreDataFuzzy: true // 不进行数据脱敏处理
  };
  for (let dim of selectDimensions) {
    // 和 server/action/data-model.ts 中 getSelectDimensionsValue 类似
    // 实现 distinct 的效果，并且对数据做了从小到大的排序
    limit = limit || 1000;
    // if (dimsMap[dim.id]?.dataTypeInDB?.toLowerCase().includes('text')) {
    //   limit = 100;
    // }
    const params: DataModelParams = {
      type: 'dimension_select',
      needDistinct: true,
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'DIMVAL', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: dim.id,
              type: 'd',
              sort: 'asc'
            }
          ],
          limit
        } as any,
        notNeedWhere: true // 获取所有的维度取值，不加任何where限制
      }
    };
    const result = await dataModelProxy(fakeRequest, group, null, params, false);
    // 获取到值时才去存储
    if ((result as DataModelProxyReturn).dataset?.length) {
      res[dim.id] = [];
      ((result as DataModelProxyReturn).dataset || []).forEach(item => {
        if (item.DIMVAL !== null && item.DIMVAL !== undefined && item.DIMVAL !== '') {
          let val = typeof item.DIMVAL === 'string' ? item.DIMVAL.slice(0, 100) : item.DIMVAL; // string类型截取前100个字符，防止内容过大
          res[dim.id].push(val);
        }
      });
    }
  }
  return res;
}

/**
 * 获取某一个数据模型中每个日期类型维度的起始时间，返回的数据是一个使用维度的id做key的对象
 * @param dataModel 数据模型
 * @param company 当前组织
 */
export async function getDimDateBeginData(
  dataModel: IDataModelInstance,
  company: ICompanyInstance,
  group?: IGroupInstance
) {
  const dataModelSchema: DataModelSchema = dataModel.config as any;
  if (!dataModelSchema.tables || !dataModelSchema.tables.length) {
    return {};
  }
  const dimsMap = dataModelSchema.dimensions;
  const selectDimensions: ISelectDimension[] = [];
  // 查询所有的维度，包括了计算维度
  dataModelSchema.dimensionMenu.forEach(menu => {
    menu.nodes.forEach(nodeId => {
      const dim = dimsMap[nodeId];
      if (dim) {
        if (
          dim.type === 'dimension' &&
          !dim.isHidden &&
          (dim.dataType === 'date' ||
            dim.dataType === 'datetime' ||
            dim.dataType === 'timestamp' ||
            dim.convert.type === 'date' ||
            dim.convert.type === 'datetime')
        ) {
          selectDimensions.push({
            id: nodeId,
            frontendId: nodeId,
            type: 'd'
          });
        } else if (dim.type === 'hierarchy' && dim.pathIds && dim.pathIds.length) {
          dim.pathIds.forEach(subNodeId => {
            const subDim = dimsMap[subNodeId];
            if (
              subDim.type === 'dimension' &&
              !subDim.isHidden &&
              (dim.dataType === 'date' ||
                dim.dataType === 'datetime' ||
                dim.dataType === 'timestamp' ||
                dim.convert.type === 'date' ||
                dim.convert.type === 'datetime')
            ) {
              selectDimensions.push({
                id: subNodeId,
                frontendId: subNodeId,
                type: 'd'
              });
            }
          });
        }
      }
    });
  });

  let res: FieldsData = {};
  // 将上面注释掉的代码改成了以下的方式来实现，因为要支持跨数据源的数据模型，所有的逻辑都统一在 data-model-proxy.ts 中来实现
  const customTable = await getCustomTablesByHash(dataModel.hash);
  let dummyReq = {
    company,
    productConfig: {} as any
  };
  if (yog.conf.sugar.deployType === 'saas') {
    loadProductConfig(dummyReq);
  }
  if (!group) {
    group = (await dataModel.getGroup()) as IGroupInstance;
  }
  const fakeRequest: IDataModelProxyFakeRequest = {
    ...dummyReq,
    ignoreDataLineLimits: true, // 忽略行级别权限的限制
    ignoreDataFuzzy: true // 不进行数据脱敏处理
  };
  for (let dim of selectDimensions) {
    // 和 server/action/data-model.ts 中 getSelectDimensionsValue 类似
    // 利用 group by 来实现 distinct 的效果，并且对数据做了从小到大的排序
    let params: DataModelParams = {
      type: 'dimension_select',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'DIMVAL', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: dim.id,
              type: 'd',
              sort: 'asc'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true // 获取所有的维度取值，不加任何where限制
      }
    };
    let result = await dataModelProxy(fakeRequest, group, null, params, false);
    // status为808是未查询到数据的情况的状态码，和以前的404是一样的
    if (result.status && ![404, 808].includes(result.status)) {
      throw new CommonError(result.msg);
    }
    let min = +new Date(((result as DataModelProxyReturn).dataset || []).map(item => item.DIMVAL)[0]);
    params = {
      type: 'dimension_select',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'DIMVAL', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: dim.id,
              type: 'd',
              sort: 'desc'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true // 获取所有的维度取值，不加任何where限制
      }
    };
    result = await dataModelProxy(fakeRequest, group, null, params, false);
    // status为808是未查询到数据的情况的状态码，和以前的404是一样的
    if (result.status && ![404, 808].includes(result.status)) {
      throw new CommonError(result.msg);
    }
    let max = +new Date(((result as DataModelProxyReturn).dataset || []).map(item => item.DIMVAL)[0]);
    if (isNaN(min) || isNaN(max)) {
      min = 1;
      max = 1;
    }
    res[dim.id] = [min, max];
  }

  return res;
}

export async function statisticsDataModel(
  dataModelSchema: DataModelSchema,
  dimRes: FieldsData,
  dateTimeRes: FieldsData,
  group?: IGroupInstance | null
) {
  let i = 0;
  let dimensions: PlainObject = {};
  const calculatedDimIdsMap: Array<String> = [], // 所有计算维度id集合
    dimensionIdsMap: Array<String> = []; // 非计算字段id集合
  const dimsMap = dataModelSchema.dimensions;
  // 按照数据模型编辑页面中展示的上下顺序进行遍历，确保最上面的维度优先被统计到示例数据
  for (const menu of dataModelSchema.dimensionMenu) {
    if (menu.predictType) {
      continue;
    }
    menu.nodes.forEach(nodeId => {
      const dimObj = dimsMap[nodeId];
      if (dimObj) {
        if (dimObj.type === 'dimension') {
          if (dimObj.calculated) {
            calculatedDimIdsMap.push(nodeId);
          } else {
            dimensionIdsMap.push(nodeId);
          }
        } else if (dimObj.type === 'hierarchy' && dimObj.pathIds && dimObj.pathIds.length) {
          dimObj.pathIds.forEach(subNodeId => {
            const subDim = dimsMap[subNodeId];
            if (subDim && subDim.type === 'dimension') {
              if (subDim.calculated) {
                calculatedDimIdsMap.push(subNodeId);
              } else {
                dimensionIdsMap.push(subNodeId);
              }
            }
          });
        }
      }
    });
  }

  // 先存储计算字段的值，再存储普通维度字段
  calculatedDimIdsMap.concat(dimensionIdsMap).forEach((dimId: string) => {
    if (dimRes[dimId]) {
      // 存储前200个维度的10个值，之后用在智能问数的prompt提示中， （旧版的Sugar的智能问数下线了，就不需要存储300个取值了，所以这里只存储10个用于给新版智能问数的策略那块使用）
      // 1、得判断维度的数据类型吧，例如日期、日期时间类型的就不需要存储。
      // 2、另外，有的字符串维度的取值可能非常长，用户在维度中存了一篇大长文，这时需要做截断（否则长度回非常长导致存储内容太大），建议截断前30字符串。
      // 3、还有在分析中隐藏的维度，应该也不需要存储取值
      const dim = dataModelSchema.dimensions[dimId];
      // 不需要存储维度详情值的维度名称
      const dimNameBlackList = [
        /^(id|__id__)$/i, // 如：id、ID、Excel数据源中自动加的__id__字段
        /\w(Id|_id|-id)$/, // 如 clientId、user-id、group_id
        /[^x00-xff](id)$/i // 如 用户id
      ];
      let notStorage = false;
      for (let j = 0; j < dimNameBlackList.length; j++) {
        if (dimNameBlackList[j].test(dim.field)) {
          notStorage = true;
          break;
        }
      }
      if (
        i < 200 &&
        !dim.isHidden &&
        !['date', 'datetime', 'timestamp'].includes(dim.convert.type || dim.dataType) &&
        !notStorage
      ) {
        i++;
        dimensions[dimId] = [];
        for (let j = 0; j < dimRes[dimId].length; j++) {
          if (j >= 10) {
            break;
          }
          const dimResVal = dimRes[dimId][j];
          if (dimResVal !== null && dimResVal !== undefined && dimResVal !== '') {
            dimensions[dimId].push(String(dimResVal).slice(0, 30));
          }
        }
        // 如果没存储值就把当前记录的维度信息去掉
        if (!dimensions[dimId].length) {
          delete dimensions[dimId];
        }
      }
      dataModelSchema.dimensions[dimId].statistics = {
        distinctCount: dimRes[dimId].length
      };
    } else if (dateTimeRes[dimId]) {
      dataModelSchema.dimensions[dimId].statistics = {
        beginTime: dateTimeRes[dimId][0],
        endTime: dateTimeRes[dimId][1]
      };
    } else {
      dataModelSchema.dimensions[dimId].statistics = undefined;
    }
  });
  dataModelSchema.hasRunStatistics = true;
  if (Object.keys(dimensions)?.length && group) {
    let dimensionStatisticsValueKVHash = dataModelSchema.dimensionStatisticsValueKVHash || '';
    // dimensionStatisticsValueKVHash有值，先去kv表里查一下，防止找不到这个hash不能进行修改也不能新增
    if (dimensionStatisticsValueKVHash) {
      const kvI = await KV.findOne({
        where: {
          hash: dimensionStatisticsValueKVHash,
          group_id: group?.id
        }
      });
      if (!kvI) {
        dimensionStatisticsValueKVHash = '';
      }
    }
    const data = await setKV(
      {
        content: JSON.stringify(dimensions),
        group_id: group?.id,
        type: 'json',
        hash: dimensionStatisticsValueKVHash
      },
      group?.hash || ''
    );
    dataModelSchema.dimensionStatisticsValueKVHash = data.hash || '';
  }
}
