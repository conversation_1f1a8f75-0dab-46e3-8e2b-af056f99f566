/**
 * @file 问数配置service
 * <AUTHOR>
 */

import {Request} from '../lib/types';
import {
  IDataModelInstance,
  IGroupInstance,
  IDataModelLearnInstance,
  ITaskInstance,
  ITaskAttr,
  Task,
  DataModelLearn,
  DataModel,
  DefaultDataModel,
  Group,
  DataModelUsage,
  IUserInstance,
  ICompanyInstance,
  RoleReport,
  UserReport,
  GBIDMLearn,
  GBIIframeConfig
} from '../models';
import {getHash} from '../lib/hashids';
import {getMaxOfId} from './model-common';
import {CommonError} from '../lib/errors';
import * as axios from 'axios';

// 10秒后开始学习任务
export const DATA_MODEL_LEARN_DELAY = 10 * 1000;
const {deployType, gbi} = yog.conf.sugar;

// 检查当前版本是否支持智能问数
export const checkAIAssistant = (req: Request, justCheck = false) => {
  // 测试中
  return true;
  let enable = false;

  if (deployType === 'saas') {
    enable = req.company.service_type === 'advan';
  } else if (gbi?.enableAIAssistant && gbi.ESDataIndexURL) {
    enable = true;
  }

  if (!justCheck && !enable) {
    throw new CommonError('当前版本不支持智能问数');
  }

  return enable;
};

// 创建多个学习任务
export const createLearnTasks = async (
  items: {
    dmLearn: IDataModelLearnInstance;
    dataModel: IDataModelInstance;
    group: IGroupInstance;
    LEARN_TIME_DELAY?: number | undefined;
    bulkCreate?: boolean;
    dmLearnAttr?: PlainObject;
  }[],
  transaction: any
) => {
  let rows: ITaskInstance[] = [];

  if (!items.length) {
    return [];
  }

  const maxId = await getMaxOfId('sugar_tasks');

  const createAttrs: ITaskAttr[] = items.map((item, index) => {
    const {dmLearn, dataModel, group, LEARN_TIME_DELAY, bulkCreate, dmLearnAttr} = item;
    return {
      type: 'dataModel-es',
      hash: getHash('task_', maxId + index + 1),
      priority: 3,
      config: JSON.stringify({
        companyId: group.company_id,
        group: {
          groupId: group.id,
          groupHash: group.hash
        },
        dmLearn: {
          id: dmLearn.id,
          hash: dmLearn.hash
        },
        dataModel: {
          id: dataModel.id,
          hash: dataModel.hash
        },
        bulkCreate: bulkCreate || false,
        dmLearnAttr: dmLearnAttr
      }),
      status: 0,
      group_id: group.id,
      schedule_id: dataModel.id,
      // 10秒后开始任务
      run_at: new Date(Date.now() + DATA_MODEL_LEARN_DELAY + (LEARN_TIME_DELAY || 1) + index * 1000)
    };
  });

  rows = await Task.bulkCreate(createAttrs, {transaction});

  return rows;
};

// 数据模型发生改动，有问数配置的学习任务需要重新开始学习
export const updateDMLearnTask = async ({dataModel, group}: {dataModel: IDataModelInstance; group: IGroupInstance}) => {
  // return false;
  console.log('start-learn111111111111111111111111');
  const dmLearn = await DataModelLearn.findOne({
    where: {
      group_id: group.id,
      data_model_id: dataModel.id
    }
  });

  const gbiDMLearn = await GBIDMLearn.findOne({
    where: {
      group_id: group.id,
      data_model_id: dataModel.id
    }
  });
  console.log('dmLearn-------', dmLearn, group.id, dataModel.id);
  console.log('gbiDMLearn--------', gbiDMLearn);
  if (!dmLearn && !gbiDMLearn) {
    console.log('end-learn111111111111111111111111');
    return;
  }

  const taskConfig = JSON.stringify({
    companyId: group.company_id,
    group: {
      groupId: group.id,
      groupHash: group.hash
    },
    dmLearn: dmLearn
      ? {
          id: dmLearn.id,
          hash: dmLearn.hash
        }
      : undefined,
    gbiDMLearn: gbiDMLearn
      ? {
          id: gbiDMLearn.id,
          hash: gbiDMLearn.hash
        }
      : undefined,
    dataModel: {
      id: dataModel.id,
      hash: dataModel.hash
    }
  });

  const [dmLearnTask, created] = await Task.findOrCreate({
    where: {
      type: 'dataModel-es',
      group_id: group.id,
      schedule_id: dataModel.id
    },
    defaults: {
      type: 'dataModel-es',
      priority: 3,
      config: taskConfig,
      status: 0,
      group_id: group.id,
      schedule_id: dataModel.id,
      // 10秒后开始任务
      run_at: new Date(Date.now() + DATA_MODEL_LEARN_DELAY)
    }
  });

  if (created) {
    dmLearnTask.hash = getHash('task_', dmLearnTask.id);
  } else {
    // 修改任务状态和config，回到任务未开始的状态和配置
    dmLearnTask.status = 0;
    dmLearnTask.config = taskConfig;
    dmLearnTask.run_at = new Date(Date.now() + DATA_MODEL_LEARN_DELAY);

    await dmLearnTask.save();
  }
  console.log('task created', created);

  if (dmLearn) {
    dmLearn.status = 0;
    await dmLearn.save();
  }
  if (gbiDMLearn) {
    gbiDMLearn.status = 0;
    await gbiDMLearn.save();
  }
};

// 删除数据模型学习和任务
export const deleteDMLearnTask = async (dmIds: number[], dmHashs: string[], force = false, transaction: any) => {
  await DataModelLearn.destroy({
    where: {
      data_model_id: {
        $in: dmIds
      }
    },
    force,
    transaction
  });

  await GBIDMLearn.destroy({
    where: {
      data_model_id: {
        $in: dmIds
      }
    },
    force,
    transaction
  });

  await Task.destroy({
    where: {
      type: 'dataModel-es',
      schedule_id: {
        $in: dmIds
      }
    },
    force,
    transaction
  });

  // 配置了数据索引的url,删除数据索引
  if (yog.conf.sugar.gbi.ESDataIndexURL) {
    try {
      const ESDataIndexURL = new URL(yog.conf.sugar.gbi.ESDataIndexURL);
      const ESDataIndexURLDomain = `${ESDataIndexURL.protocol}//${ESDataIndexURL.hostname}:${ESDataIndexURL.port}`;
      const ESDataIndexURL_delete = `${ESDataIndexURLDomain}/gbi/datamodel/batch/del`;
      let dataBody: PlainObject = {hashIds: dmHashs};
      const result = await axios.default.post(`${ESDataIndexURL_delete}`, dataBody, {
        timeout: 500000
      });
      if (!result.data || result.data.status !== 0) {
        yog.log.warning(`[delete_data_index_error] ${dmHashs}`);
      }
    } catch (e) {
      yog.log.warning(`[delete_data_index_error] ${dmHashs} error ：` + e);
    }
  }
};

// 校验多个数据模型是否可用于问数，支持跨空间
export const canUseDMLearns = async (
  list: any[],
  {
    user,
    company,
    checkByHash,
    shareId
  }: {
    user: IUserInstance;
    company: ICompanyInstance;
    checkByHash?: boolean;
    shareId?: string;
  }
): Promise<IDataModelInstance[]> => {
  const rows: IDataModelInstance[] = [];
  let iframeScopeHash: string[] = [];
  let shareType = 'token';

  if (!user || !company) {
    throw new CommonError('检查数据模型权限的参数错误');
  }

  if (shareId) {
    // 取发布完成的iframe
    const iframe = await GBIIframeConfig.findOne({
      where: {
        company_id: company.id,
        share_id: shareId,
        status: [1, 2]
      }
    });
    if (iframe) {
      const iframeJson = iframe.toJSON() as PlainObject;
      iframeScopeHash = (iframeJson?.releaseConfig?.dataScope || []).map((item: PlainObject) => item.hash);
      shareType = iframeJson?.releaseConfig?.shareType;
      if (shareType !== 'token') {
        const dmList = await DataModel.findAll({
          where: {
            type: 1,
            hash: {
              $in: iframeScopeHash.filter(str => !!str)
            }
          },
          attributes: {
            exclude: ['config']
          },
          order: [['created_at', 'desc']],
          include: [
            {
              model: Group,
              as: 'group',
              attributes: {
                exclude: ['config']
              }
            },
            {
              model: DataModelLearn,
              as: 'dmLearn',
              where: {
                status: 2
              }
            },
            {
              model: DefaultDataModel,
              as: 'defaultDM',
              separate: true,
              where: {
                company_id: company.id
              }
            },
            {
              model: DataModelUsage,
              as: 'dmUsage',
              separate: true,
              where: {
                page_type: 'aiAsk',
                company_id: company.id
              }
            }
          ]
        });
        return dmList;
      }
    }

    // 没有配置iframe的数据模型范围或者未发布，直接返回一个空数组
    if (iframeScopeHash.length === 0) {
      return [];
    }
  }

  if (checkByHash) {
    list = await DataModel.findAll({
      where: {
        type: 1,
        hash: {
          $in: list.filter(str => !!str)
        }
      },
      attributes: {
        exclude: ['config']
      },
      include: [
        {
          model: DataModelLearn,
          as: 'dmLearn',
          attributes: {
            exclude: ['config']
          },
          where: {
            status: 2
          }
        },
        {
          model: Group,
          as: 'group',
          attributes: {
            exclude: ['config']
          },
          where: {
            id: {
              $not: null
            }
          }
        }
      ]
    });
  }

  // 当前组织下用户加入的所有空间
  const groups = await user.getGroups({
    where: {
      company_id: company.id
    }
  });

  const guMap: PlainObject = {};
  groups.forEach(item => {
    guMap[item.id!] = (item as any).GroupUser;
  });

  const checkList: any[] = [];
  list.forEach(item => {
    const groupUser = guMap[item.group_id];
    // 空间管理员、示例数据模型不校验权限配置
    if (groupUser?.group_admin || (gbi.exampleDMHashes.length && gbi.exampleDMHashes.includes(item.hash))) {
      rows.push(item);
    } else if (groupUser) {
      checkList.push(item);
    }
  });

  if (checkList.length) {
    const groupIds = groups.map(item => item.id);
    const roles = await user.getRoles({
      where: {
        group_id: {
          $in: checkList.map(item => item.group_id)
        }
      }
    });
    const roleMap: PlainObject = {};
    roles.forEach(item => {
      roleMap[item.id] = item;
    });

    const roleReports = await RoleReport.findAll({
      where: {
        role_id: {
          $in: roles.map(item => item.id)
        }
      }
    });

    const limitMap: PlainObject = {};
    roleReports.forEach(item => {
      const role = roleMap[item.role_id];
      if (!role) {
        return;
      }

      if (item.resource_limit_type === 'superAIAnalysis') {
        limitMap[role.group_id] = true;
      } else if (item.resource_limit_type === 'dmLearnUse') {
        if (!limitMap[role.group_id]) {
          limitMap[role.group_id] = {
            [item.report_id]: true
          };
        } else if (typeof limitMap[role.group_id] === 'object') {
          limitMap[role.group_id][item.report_id] = true;
        }
      }
    });

    const userReports = await UserReport.findAll({
      where: {
        user_id: user.id,
        group_id: {
          $in: groupIds
        }
      }
    });

    userReports.forEach(item => {
      if (item.resource_limit_type === 'superAIAnalysis') {
        limitMap[item.group_id] = true;
      } else if (item.resource_limit_type === 'dmLearnUse') {
        if (!limitMap[item.group_id]) {
          limitMap[item.group_id] = {
            [item.report_id]: true
          };
        } else if (typeof limitMap[item.group_id] === 'object') {
          limitMap[item.group_id][item.report_id] = true;
        }
      }
    });

    checkList.forEach(item => {
      if (!item.dmLearn || item.dmLearn.status !== 2) {
        return;
      }

      const inGroup = limitMap[item.group_id];
      if (inGroup === true || (inGroup && inGroup[item.dmLearn.id])) {
        rows.push(item);
      }
    });
  }

  if (iframeScopeHash.length > 0) {
    let {exampleDMHashes = []} = gbi;
    let currentIframeScopeHash = Array.from(new Set([...exampleDMHashes, ...iframeScopeHash]));
    return rows.filter(item => currentIframeScopeHash.includes(item.hash));
  }
  return rows;
};

export interface LearnedDMListParams {
  user: IUserInstance;
  company: ICompanyInstance;
  groupId?: number;
  maxLength?: number;
  dmHashes?: string[];
  excludeHashes?: string[];
  shareId?: string;
}

// 当前组织下可供当前用户使用的学习成功的数据模型列表
// 优先级排序，默认，最近使用，最后是普通的
// 支持按groupId筛选，支持取前maxLength个
// 如果是iframe配置了数据范围，则只返回在数据范围内切有权限的数据模型
export const learnedDMList = async ({
  user,
  company,
  groupId,
  maxLength,
  dmHashes = [],
  excludeHashes = [],
  shareId
}: LearnedDMListParams): Promise<IDataModelInstance[]> => {
  const dmWhere: PlainObject = {type: 1};
  if (dmHashes?.length) {
    dmWhere.hash = {
      $in: dmHashes
    };
  } else if (excludeHashes?.length) {
    dmWhere.hash = {$notIn: excludeHashes};
  }

  let group_id: any = 0;
  if (groupId) {
    group_id = groupId;
  } else {
    const groups = await user.getGroups({
      where: {
        company_id: company.id
      }
    });

    if (groups.length) {
      group_id = {$in: groups.map(item => item.id)};
    }
  }

  const dmList = await DataModel.findAll({
    where: dmWhere,
    attributes: {
      exclude: ['config']
    },
    order: [['created_at', 'desc']],
    include: [
      {
        model: Group,
        as: 'group',
        attributes: {
          exclude: ['config']
        },
        where: {
          id: group_id
        }
      },
      {
        model: DataModelLearn,
        as: 'dmLearn',
        where: {
          status: 2
        }
      },
      {
        model: DefaultDataModel,
        as: 'defaultDM',
        separate: true,
        where: {
          company_id: company.id,
          user_id: user.id
        }
      },
      {
        model: DataModelUsage,
        as: 'dmUsage',
        separate: true,
        where: {
          page_type: 'aiAsk',
          company_id: company.id,
          user_id: user.id
        }
      }
    ]
  });

  let checkedList = await canUseDMLearns(dmList, {
    user,
    company,
    shareId
  });
  let {exampleDMHashes = []} = gbi;
  if (excludeHashes?.length && checkedList.length) {
    // 排除示例数据模型后可用的数量为0才能加示例数据模型
    exampleDMHashes = [];
  }

  if (exampleDMHashes.length) {
    const hashSet = new Set(checkedList.map(item => item.hash));
    let exampleHashes = exampleDMHashes.filter(hash => {
      if (dmHashes?.length) {
        // 如果是按指定的数据模型hashes查询的，需要检查checkedList里是否已经有
        // 有就不需要再加示例数据模型
        // 没有的再检查指定的hashes，有指定的再加
        return !hashSet.has(hash) && dmHashes.includes(hash);
      }

      return !hashSet.has(hash);
    });

    if (exampleHashes.length) {
      const exampleDMList = await DataModel.findAll({
        where: {
          hash: {$in: exampleHashes}
        },
        attributes: {
          exclude: ['config']
        },
        order: [['created_at', 'desc']],
        include: [
          {
            model: Group,
            as: 'group',
            attributes: {
              exclude: ['config']
            },
            where: {
              id: {
                $not: null
              }
            }
          },
          {
            model: DataModelLearn,
            as: 'dmLearn',
            where: {
              status: 2
            }
          },
          {
            model: DefaultDataModel,
            as: 'defaultDM',
            separate: true,
            where: {
              company_id: company.id,
              user_id: user.id
            }
          },
          {
            model: DataModelUsage,
            as: 'dmUsage',
            separate: true,
            where: {
              page_type: 'aiAsk',
              company_id: company.id,
              user_id: user.id
            }
          }
        ]
      });
      if (exampleDMList.length) {
        checkedList = checkedList.concat(exampleDMList);
      }
    }
  }

  checkedList.forEach(item => {
    const {defaultDM, dmUsage} = item;
    let orderTime;
    // 有设置过默认又有使用记录的需要加权系数5
    if (defaultDM[0] && dmUsage[0]) {
      orderTime = 5 * Math.max(defaultDM[0].use_time.getTime(), dmUsage[0].use_time.getTime());
    } else if (defaultDM[0]) {
      // 有设置过默认的需要加权系数4
      orderTime = 4 * defaultDM[0].use_time.getTime();
    } else if (dmUsage[0]) {
      // 没有设置过默认，但有使用记录的加权系数3
      orderTime = 3 * dmUsage[0].use_time.getTime();
    } else if (exampleDMHashes.length && !exampleDMHashes.includes(item.hash)) {
      // 不是示例的加权系数2
      orderTime = 2 * (item.created_at as any).getTime();
    } else {
      // 示例的又没有使用过放列表的最后
      orderTime = (item.created_at as any).getTime();
    }
    (item as PlainObject).orderTime = orderTime;
  });

  checkedList.sort((prev: PlainObject, next: PlainObject) => next.orderTime - prev.orderTime);

  return maxLength ? checkedList.slice(0, maxLength) : checkedList;
};
