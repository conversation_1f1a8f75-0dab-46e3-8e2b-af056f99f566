/**
 * @file 路由
 * <AUTHOR>
 */

import shareSourceDomain from './middlewares/share-source-domain';
import i18n from './middlewares/i18n';
import login, {jsonParse} from './middlewares/login';
import csrf from './middlewares/csrf';
// import authorize from './middlewares/authorize';
import log from './middlewares/log';
import migrationCheck from './middlewares/migration-check-and-mpp-host-update';
import dashboardReportShare from './middlewares/dashboard-report-share';
import saasCompany from './middlewares/saas-company';
import apiLog from './middlewares/api-log';
import stat from './middlewares/stat';
import idaasAudit from './middlewares/idaas-audit';
import {NotFoundError, CommonError} from './lib/errors';
import * as _ from 'lodash';
import {Request, Response, NextFunction, PlainObject, Router} from './lib/types';
import {promClient, sugarRequestCounter, sugarRequestErrorCounter} from './service/prometheus';
import {getDateByString, isValidDate, formatDate, isPrivateAdvan} from './lib/common-helper';
import * as crypto from 'crypto';
import * as os from 'os';
import {
  User,
  IUserInstance,
  Slider,
  Company,
  Group,
  Report,
  Dashboard,
  ICompanyInstance,
  DataPortal,
  IGroupInstance,
  IGroupUserInstance
} from './models/index';
import Authorize, {IAuthCanReadGroupDataPortalReturn, IAuthCanEditDataPortalReturn} from './lib/authorize/index';
import {md5} from './lib/sugar-domain';
import {databaseType} from './data-adapter/data-model-proxy/db-driver';
import {language} from './service/language/index';
import {showOrHideVersionSwitch} from './service/switch-between-old-and-new';
const xmlparser = require('express-xml-bodyparser');
import openApiAuthorize from './middlewares/openapi-authorize';
import openApiProxy from './middlewares/openapi-proxy';
import openApiSystem from './middlewares/openapi-system';
import openApiInternal from './middlewares/openapi-internal';
import DebuggerAllApiProxy from './middlewares/debugger_all_api_proxy';
import fs = require('fs');
import {sweepLogAndTmp} from './service/sweep-log-and-tmp';
import {getExpireInfo, productTypeMap} from './service/company';

const bytenode = require('bytenode');
let paramsObjFn: any;
if (os.arch().includes('arm') || os.arch().includes('aarch')) {
  paramsObjFn = require('./action/manage/mpp/mpp-lock-a.jsc').paramsObjFn;
} else {
  paramsObjFn = require('./action/manage/mpp/mpp-lock-i.jsc').paramsObjFn;
}
import {getHasReadLimitMenus, getFormatMenus} from './service/data-portal';

/**
 * 该函数从./lib/common-helper中复制而来，为了增加router的复杂度防止破解所以复制了一遍
 */
const isSaaSDomain = (hostname: string) => {
  hostname = hostname.replace('http://', '').replace('https://', '');
  if (
    hostname === 'sugar.baidu.com' ||
    hostname === 'sugartest.bce.baidu.com' ||
    hostname === 'sugar.bce.baidu.com' ||
    hostname === 'sugar.baidubce.com' ||
    hostname === 'sugar.aipage.com' ||
    hostname === 'gbi.aipage.com' ||
    hostname === 'gbi.bce.baidu.com' ||
    hostname === 'sugar.bcetest.baidu.com'
  ) {
    return true;
  }
  return false;
};

type IRegisterActionToRouter = (
  action: string,
  path?: string,
  otherNotGetMethod?: {
    [propName: string]: 'get' | 'post' | 'put' | 'delete';
  },
  otherWithHashMethod?: {
    [propName: string]: boolean;
  }
) => void;

export default (router: Router) => {
  let aaa: any = {};

  const wrapAsync = router.wrapAsync;

  // csrf middleware
  router.use(wrapAsync(csrf));
  router.use(xmlparser());

  const paramsObj = paramsObjFn(
    Company,
    router,
    Group,
    jsonParse,
    User,
    sugarRequestCounter,
    Report,
    isSaaSDomain,
    Dashboard,
    CommonError,
    log,
    Authorize,
    md5,
    aaa
  );

  if (yog.conf.sugar.apiLog?.enable) {
    router.use(wrapAsync(apiLog));
  }

  // 返回后端获取到的domain和headers，方便license那块的验证调试，测试nginx代理转发时的 proxy_set_header Host $http_host; 是否设置成功
  router.get(
    '/server-domain-and-headers',
    wrapAsync(async (req: Request, res: Response) => {
      let remoteAddress = req.connection.remoteAddress;
      if (remoteAddress && remoteAddress.includes('::ffff:')) {
        remoteAddress = remoteAddress.split(':').reverse()[0];
      }
      const ret: PlainObject = {};
      const currUserCount = await User.count();
      ret['company-' + 'name'] = aaa['company' + 'Name'];
      ret['expire' + '-time'] = aaa['la' + 'st' + 'D' + 'ay'];
      ret['license-domain'] = aaa['domain'];
      ret['user-' + 'count'] = aaa['userC' + 'ount'];
      ret['current-us' + 'er-count'] = currUserCount;
      ret['ty' + 'pe'] = aaa['t'] === 'prod' ? '正式' : '试用';
      ret['d' + 'i'] = !!aaa['d' + 'i'];
      ret['ernieAsk'] = !!aaa['ernieAsk'];
      ret['detail'] = process.env.sugar_license?.slice(0, 10) + '******';

      if (!(global as any).sugarVersion) {
        let changeLog = fs.readFileSync('./views/docs/Changelog/index.tpl', 'utf-8') || '';
        let lastedTagInfo = changeLog.match(
          /<\/a>百度GBI V([\d\.X]+)<\/h2>[\s\S]+?<p>(.+?)<\/p>[\s\S]+?<ul>([\s\S]+?)<\/ul>/
        );
        if (lastedTagInfo) {
          (global as any).sugarVersion = 'GBI V' + lastedTagInfo[1];
        } else {
          lastedTagInfo = changeLog.match(/<\/a>V([\d\.X]+)<\/h2>[\s\S]+?<p>(.+?)<\/p>[\s\S]+?<ul>([\s\S]+?)<\/ul>/);
          if (lastedTagInfo) {
            (global as any).sugarVersion = lastedTagInfo[1];
          }
        }
      }
      ret.version = (global as any).sugarVersion || '-';

      const isPrivate = yog.conf.sugar.deployType === 'private';
      return res.json({
        'client-ip': req.header('x-forwarded-for') || remoteAddress || '', // 用户的ip地址
        'domain': req.hostname,
        'machine-datetime': formatDate(new Date(), '$1-$2-$3 $4:$5:$6'),
        'feature-switch': {
          login_type: (process.env.sugar_login_type || 'demo').trim().toLowerCase(),
          enable_outside_email: !!process.env.sugar_email_host,
          enable_outside_redis: !!process.env.sugar_redis_host,
          enable_upload_excel:
            process.env.sugar_enable_mpp === '1' ||
            process.env.sugar_enable_mpp === 'true' ||
            process.env.sugar_enable_mpp === 'yes' ||
            process.env.sugar_enable_mpp === 'ok',
          enable_data_easy_fetch:
            process.env.sugar_enable_data_easy_fetch === '1' ||
            process.env.sugar_enable_data_easy_fetch === 'true' ||
            process.env.sugar_enable_data_easy_fetch === 'yes' ||
            process.env.sugar_enable_data_easy_fetch === 'ok',
          enable_federated: process.env.sugar_federated_host ? true : false,
          enable_ml_train: !!process.env.sugar_ml_train_endpoint,
          enable_ernie: !!(process.env.sugar_ernie_client_secret || process.env.sugar_ernie_accesscode),
          ernie_chat_url: isPrivate ? process.env.sugar_ernie_chat_url || 'EB Default' : '******' // 云上SaaS版不暴露EB调用的接口地址
        },
        'license-info': isPrivate ? ret : {}, // 云上SaaS版不暴露license信息
        'request-headers': {
          ...req.headers,
          cookie: '******', // cookie不暴露
          bfeip: isPrivate ? req.headers.bfeip : '******',
          bfe_logid: isPrivate ? req.headers.bfe_logid : '******'
        }
      });
    })
  );

  if (yog.conf.sugar.deployType === 'saas') {
    router.get('/console/cdnlog', wrapAsync(router.action('console/cdnlog').createAndGoToReport));
    router.post('/console/cdnlog', wrapAsync(router.action('console/cdnlog').createAndGoToReport));
    router.options('/console/cdnlog', wrapAsync(router.action('console/cdnlog').preFlight));
    router.get('/cloud/canUserBuy', wrapAsync(router.action('cloud/cloud').canUserBuy));
    router.options('/cloud/canUserBuy', wrapAsync(router.action('cloud/cloud').preFlight));
  }

  // 方便前端开发调试，让所有 /api/***** 这类接口都proxy到测试环境，html、js等静态文件还走本地的
  if (process.env.sugar_debugger_all_api_proxy_endpoint) {
    router.use(wrapAsync(DebuggerAllApiProxy));
  }

  // 判断sugar后端的mysql数据库结构是否和当前sugar版本match
  router.use(wrapAsync(migrationCheck));

  // 分享的大屏、报表、轮播页、图片等资源，saas版下需要切换到非baidu域名
  router.use(wrapAsync(shareSourceDomain));
  // 语言切换 middleware
  router.use(wrapAsync(i18n));
  // 登录middleware
  router.use(wrapAsync(login));
  // SaaS 版本 验证组织是否有效
  router.use(wrapAsync(saasCompany));
  // 权限验证middleware
  router.use(wrapAsync(paramsObj.authorize));
  // openApi权限认证
  router.use(wrapAsync(openApiAuthorize));
  // internal openApi 认证
  router.use(openApiInternal);
  // proxy代理服务的验证
  router.use(openApiProxy);
  // 系统 openapi 验证
  router.use(openApiSystem);
  // 大屏和报表分享相关的middleware
  router.use(wrapAsync(dashboardReportShare));
  // 日志middleware
  router.use(paramsObj.log);
  // 统计middleware
  router.use(stat);
  // idaas 审计
  router.use(idaasAudit);
  /**
   * 将action中的各个方法注册到router上。默认将action文件中的 get、post、show、put、remove 方法做了restful的路由注册。
   * @param {string} action action文件
   * @param {string} path action所对应url的path，默认是在action前面加`/api/`前缀
   * @param {Object} otherNotGetMethod 其他非restful的API的请求方式（默认是get，如果是其他就需要在这配置）
   * 如：
   * {
   *    aFunc: 'post',
   *    bFunc: 'put',
   *    cFunc: 'get'  // get可以不用配置，默认就是get
   * }
   * @param {Object} otherWithHashMethod 其他非restful的API是否在url中附加hash(id)参数
   * 其实就是：/api/chart/dFunc 和 /api/chart/chartHash/dFunc 的差别
   * {
   *    dFunc: true,
   *    eFunc: false  // false也可以不用配置
   * }
   */
  const registerAction: IRegisterActionToRouter = paramsObj.registerActionToRouter.bind(router);

  // prometheus监控项暴露的api
  router.get('/openapi/prometheus/metrics', (req: Request, res: Response) => {
    if (isSaaSDomain(req.hostname)) {
      // saas域名访问该api时，不返回具体信息，saas下的prometheus是内网ip方式访问的，并不会通过saas的域名进行访问
      res.send('Invalid, are you OK ?');
    } else {
      res.set('Content-Type', promClient.register.contentType);
      res.end(promClient.register.metrics());
    }
  });

  registerAction('openapi/system/user', '/openapi/system/user');

  // sugar实例作为proxy代理服务时，相关的 proxy api
  router.get('/openapi/proxy/test', (req: Request, res: Response) => {
    res.sugarJson('It works!');
  });
  router.post(
    '/openapi/proxy/neutron-vpc-endpoint/findOrCreate',
    wrapAsync(router.action('openapi/proxy/neutron-vpc-endpoint').findOrCreate)
  );
  router.get(
    '/openapi/proxy/neutron-vpc-endpoint/find',
    wrapAsync(router.action('openapi/proxy/neutron-vpc-endpoint').find)
  );
  router.get(
    '/openapi/proxy/neutron-vpc-endpoint/:vpcEndpointId',
    wrapAsync(router.action('openapi/proxy/neutron-vpc-endpoint').show)
  );
  router.post(
    '/openapi/proxy/neutron-vpc-endpoint/remove',
    wrapAsync(router.action('openapi/proxy/neutron-vpc-endpoint').remove)
  );
  registerAction('openapi/proxy/bce-api', '/openapi/proxy/bce-api');
  router.post('/openapi/proxy/db-proxy', wrapAsync(router.action('openapi/proxy/db-proxy').proxy));
  router.post('/openapi/proxy/api-adaptor-run', wrapAsync(router.action('openapi/proxy/api-adaptor-run').run));

  // 获取当前私有部署的license验证信息
  router.get(
    '/api/manage/' + 'company' + '-l' + 'ic',
    wrapAsync(async (req: Request, res: Response) => {
      // saas下不暴露信息
      if (yog.conf.sugar.deployType === 'saas') {
        return res.sugarJson({});
      }
      const ret: PlainObject = {};
      const currUserCount = await User.count();
      ret['com' + 'Name'] = aaa['company' + 'Name'];
      ret['e' + 'Time'] = aaa['la' + 'st' + 'D' + 'ay'];
      ret['com' + 'Co' + 'de'] = aaa['compa' + 'ny'];
      ret['user' + 'Count'] = aaa['userC' + 'ount'];
      ret['currUs' + 'erCount'] = currUserCount;
      ret['ty' + 'pe'] = aaa['t'] === 'prod' ? '正式' : '试用';
      ret['d' + 'i'] = !!aaa['d' + 'i'];

      if (!(global as any).sugarVersion) {
        let changeLog = fs.readFileSync('./views/docs/Changelog/index.tpl', 'utf-8') || '';
        let lastedTagInfo = changeLog.match(
          /<\/a>百度GBI V([\d\.X]+)<\/h2>[\s\S]+?<p>(.+?)<\/p>[\s\S]+?<ul>([\s\S]+?)<\/ul>/
        );
        if (lastedTagInfo) {
          (global as any).sugarVersion = 'GBI V' + lastedTagInfo[1];
        } else {
          lastedTagInfo = changeLog.match(/<\/a>V([\d\.X]+)<\/h2>[\s\S]+?<p>(.+?)<\/p>[\s\S]+?<ul>([\s\S]+?)<\/ul>/);
          if (lastedTagInfo) {
            (global as any).sugarVersion = lastedTagInfo[1];
          }
        }
      }
      ret.version = (global as any).sugarVersion || '-';

      res.sugarJson(ret);
    })
  );

  /* 管理中心等 后台管理相关的api */
  router.put('/api/manage/company', wrapAsync(router.action('manage/company').put));
  router.put('/api/manage/company/updateTheme', wrapAsync(router.action('manage/company').updateTheme));
  router.get('/api/manage/company/getExpire', wrapAsync(router.action('manage/company').getExpire));
  router.get('/api/manage/company/getVip', wrapAsync(router.action('manage/company').getVip));
  router.get('/api/manage/company/emailTest', wrapAsync(router.action('manage/company').emailTest));
  router.get('/api/manage/company/redisTest', wrapAsync(router.action('manage/company').redisTest));
  router.get('/api/manage/company/mppTest', wrapAsync(router.action('manage/company').mppTest));
  router.get('/api/manage/company/fetchTest', wrapAsync(router.action('manage/company').fetchTest));
  router.get('/api/manage/company/federatedTest', wrapAsync(router.action('manage/company').federatedTest));
  router.get('/api/manage/company/mlTrainTest', wrapAsync(router.action('manage/company').mlTrainTest));
  router.get('/api/manage/company/mlPredictTest', wrapAsync(router.action('manage/company').mlPredictTest));
  router.get('/api/manage/company/mlTrainReset', wrapAsync(router.action('manage/company').mlTrainReset));
  router.get('/api/manage/company/userSaas', wrapAsync(router.action('manage/company-user').getNew));
  router.post('/api/manage/company/userSaas/remove', wrapAsync(router.action('manage/company-user').removeInvite));

  registerAction('manage/company-group', '/api/manage/company/group');
  registerAction('manage/company-user', '/api/manage/company/user', {
    bulkUploadUsers: 'post',
    resetPassword: 'put',
    resetPasswordCus: 'put'
  });
  registerAction('manage/attribute', '/api/manage/attribute', {
    bulkUploadUserProps: 'post',
    quickSaveTableData: 'post',
    getPropsNameAndSchema: 'get',
    exportExcel: 'get',
    getPropsName: 'get'
  });
  registerAction('manage/company-license', '/api/manage/company/license', {
    demo: 'post'
  });

  // registerAction('manage/company-access-key', '/api/manage/company/accessKey');
  registerAction('manage/company-access-key', '/api/manage/company/:companyHash/accessKey');
  registerAction('manage/group-access-key', '/api/manage/group/:groupHash/accessKey');
  registerAction('manage/chart-warning', '/api/manage/group/:groupHash/chartWarning', {
    putUser: 'put'
  });

  registerAction(
    'manage/rename-lib',
    '/api/manage/group/:groupHash/renameLib',
    {
      multiDelete: 'put'
    },
    {
      getDetail: true
    }
  );
  registerAction('manage/sql-model', '/api/manage/group/:groupHash/sqlModel', {
    multiDelete: 'put'
  });
  registerAction(
    'manage/data-model',
    '/api/manage/group/:groupHash/dataModel',
    {
      updateOrder: 'put',
      getFormedTree: 'post',
      multiMove: 'put',
      multiDelete: 'put',
      updateFolder: 'put',
      changeDatamodelName: 'put',
      changeDataModelDatabase: 'put',
      changeDataModelQuestion: 'put',
      changeDataModelLlmconfig: 'put',
      updateNLPOpen: 'post',
      copy: 'post'
    },
    {
      changeDatamodelName: true,
      changeDataModelDatabase: true,
      changeDataModelQuestion: true,
      changeDataModelLlmconfig: true,
      updateNLPOpen: true,
      updateDMQuestion: true,
      copy: true
    }
  );
  registerAction('manage/user', '/api/manage/group/:groupHash/user', {
    multiDelete: 'put'
  });

  // 知识管理相关
  registerAction('manage/knowledge', '/api/manage/group/:groupHash/knowledge', {
    multiDelete: 'put',
    uploadPost: 'post'
  });

  registerAction('ai-ask', '/api/gbi/aiAsk', {
    getSelectDimensionsValue: 'post',
    resultDataView: 'post',
    dataModels: 'post',
    dataModelInfo: 'post'
  });

  // 问数配置相关
  registerAction('manage/data-model-learn', '/api/manage/group/:groupHash/dataModelLearn', {
    multiDelete: 'put',
    refresh: 'put',
    multiUpdate: 'put'
  });

  router.get(
    '/api/manage/dataModel/:dataModelHash/user/allUserInfo',
    wrapAsync(router.action('manage/user').allUserInfo)
  );
  router.get('/api/manage/dataModel/:dataModelHash/role/all', wrapAsync(router.action('manage/role').all));
  // router.get('/api/manage/group/:groupHash/userSearch', wrapAsync(router.action('manage/user').search));
  // router.get('/api/manage/userSearch', wrapAsync(router.action('manage/user').search));
  router.get('/api/manage/user/search', wrapAsync(router.action('manage/user').search));
  registerAction('manage/role', '/api/manage/group/:groupHash/role', {
    multiDelete: 'put'
  });
  registerAction('manage/group');
  router.post(
    '/api/manage/group/:groupHash/exportReportAndDash',
    wrapAsync(router.action('manage/group').exportReportAndDash)
  );
  router.post(
    '/api/manage/group/:groupHash/importReportAndDash',
    wrapAsync(router.action('manage/group').importReportAndDash)
  );
  router.get('/api/manage/group/:groupHash/reportExport', wrapAsync(router.action('manage/group').reportExport));
  router.get('/api/manage/group/:groupHash/dashboardExport', wrapAsync(router.action('manage/group').dashboardExport));
  router.get('/api/manage/group/getRandGroupName', wrapAsync(router.action('manage/group').getRandGroupName));
  router.get(
    '/api/manage/dataPortal/getRandPortalName',
    wrapAsync(router.action('manage/data-portal').getRandPortalName)
  );
  router.put('/api/manage/group/:groupHash/updateAIExplore', wrapAsync(router.action('manage/group').updateAIExplore));
  registerAction(
    'manage/report',
    '/api/manage/group/:groupHash/report',
    {
      updateOrder: 'put',
      multiMove: 'put',
      multiDelete: 'put',
      createFrom: 'post',
      publishDash: 'post',
      multiPublish: 'post',
      getFormedTree: 'post',
      createReportFromAnalyze: 'put'
    },
    {
      publishDash: true,
      getPublishReportData: true
    }
  );

  // 探索页面相关
  registerAction(
    'manage/explore',
    '/api/manage/group/:groupHash/explore',
    {
      updateOrder: 'put',
      multiMove: 'put',
      multiDelete: 'put',
      createFrom: 'post',
      publishDash: 'post',
      getFormedTree: 'post',
      manualGenerateDMTrainTask: 'post'
    },
    {
      publishDash: true,
      getPublishExploreData: true
    }
  );

  // 数据填报管理页面
  registerAction(
    'manage/data-submit',
    '/api/manage/group/:groupHash/dataSubmit',
    {
      updateOrder: 'put',
      multiMove: 'put',
      multiDelete: 'put',
      createFrom: 'post',
      getFormedTree: 'post',
      publishSubmit: 'post',
      writeGrantTest: 'post'
    },
    {
      publishSubmit: true
    }
  );
  registerAction(
    'manage/dashboard',
    '/api/manage/group/:groupHash/dashboard',
    {
      updateOrder: 'put',
      multiDelete: 'put',
      publishDash: 'post',
      multiPublish: 'post',
      createFrom: 'post',
      getFormedTree: 'post',
      multiMove: 'put'
    },
    {
      createFrom: true,
      generateTemplet: true,
      publishDash: true,
      getPublishDashData: true
    }
  );
  registerAction(
    'manage/scene',
    '/api/manage/group/:groupHash/scene',
    {
      updateOrder: 'put',
      multiDelete: 'put',
      createFrom: 'post'
    },
    {
      createFrom: true
    }
  );
  registerAction('manage/slider', '/api/manage/group/:groupHash/slider', {
    updateOrder: 'put',
    multiDelete: 'put'
  });
  registerAction('manage/data-portal', '/api/manage/group/:groupHash/dataPortal', {
    updateOrder: 'put',
    multiDelete: 'put'
  });
  registerAction('manage/api-auth', '/api/manage/group/:groupHash/apiAuth', {
    multiDelete: 'put',
    connectTest: 'post'
  });
  registerAction('manage/webhook', '/api/manage/group/:groupHash/webhook', {
    multiDelete: 'put',
    connectTest: 'post'
  });
  registerAction(
    'manage/database',
    '/api/manage/group/:groupHash/database',
    {
      autofill: 'post',
      connectTest: 'post',
      uploadPost: 'post',
      uploadPut: 'put',
      clearCache: 'post',
      multiDelete: 'put',
      changePassword: 'put',
      changeSk: 'put',
      updateFederatedOpen: 'post'
    },
    {
      uploadPut: true,
      clearCache: true,
      changePassword: true,
      changeSk: true,
      updateFederatedOpen: true
    }
  );
  registerAction(
    'manage/tunnel',
    '/api/manage/group/:groupHash/tunnel',
    {},
    {
      downloadTunnel: true
    }
  );

  // 组件模板添加、删除、分享、列表
  registerAction(
    'manage/widget-template',
    '/api/manage/group/:groupHash/widgetTemplate',
    {
      add: 'post',
      multiDelete: 'put',
      share: 'put'
    },
    {
      share: true
    }
  );

  // 空间页面模板添加、删除、上架、列表、导入、导出
  registerAction(
    'manage/custom-template',
    '/api/manage/group/:groupHash/customTemplate',
    {
      multiDelete: 'put',
      publish: 'put',
      exportDashOrReportToTemplate: 'post',
      importDashAndReportAsTemplate: 'post'
    },
    {
      publish: true
    }
  );

  // 模板市场相关接口
  registerAction('template-market', '/api/templateMarket');

  // 模板市场内收藏模板相关接口
  registerAction('self-template', '/api/self/template');

  // 组织页面模板添加、删除、上架、列表、导入
  registerAction(
    'manage/company-custom-template',
    '/api/manage/company/:companyHash/customTemplate',
    {
      multiDelete: 'put',
      publish: 'put',
      importDashAndReportAsTemplate: 'post'
    },
    {
      publish: true
    }
  );

  // 图表主题添加、删除、分享、列表
  registerAction(
    'manage/chart-theme',
    '/api/manage/group/:groupHash/chartTheme',
    {
      add: 'post',
      multiDelete: 'put',
      edit: 'put'
    },
    {
      info: true,
      edit: true
    }
  );
  // 图表标注添加、删除、列表
  registerAction('manage/chart-mark', '/api/chartMark/:pageType/:pageHash/chart/:chartFrontendId', {
    multiDelete: 'post'
  });

  // 回收站列表、还原、删除
  registerAction('manage/recycle', '/api/manage/group/:groupHash/recycle', {
    multiDelete: 'post',
    restore: 'post'
  });

  // 历史版本列表、更新、删除
  registerAction('manage/snapshot', '/api/snapshot/:resourceType/:resourceHash', {
    update: 'post',
    multiDelete: 'post'
  });

  // 图表评论列表、更新、删除
  registerAction('manage/chart-comment', '/api/chartComment/:pageType/:pageHash/:chartFrontendId', {
    multiDelete: 'post'
  });

  // 公开分享token认证生成访问URL
  router.post('/api/manage/share/tokenShareSign', wrapAsync(router.action('manage/token-share-sign').getUrl));

  router.get('/openapi/tunnel/cluster/:clusterName', (req: Request, res: Response) => {
    let cluster = req.params.clusterName;
    let servers = yog.conf.sugar.tunnel.clusterHost[cluster];
    if (!servers) {
      res.json({err: `没有 「${cluster}」 这个集群的配置`});
    } else {
      res.json({servers: servers});
    }
  });

  router.get('/openapi/tunnel/client/latestVersion', (req: Request, res: Response) => {
    let clientVersion = yog.conf.sugar.tunnel.clientVersion;
    if (clientVersion) {
      res.end(clientVersion);
    } else {
      res.end('-1');
    }
  });

  router.get('/openapi/private/privateLatestTag', (req: Request, res: Response) => {
    let privateLatestTag = yog.conf.sugar.privateLatestTag;
    if (privateLatestTag) {
      res.end(privateLatestTag);
    } else {
      res.end('-1');
    }
  });

  // 国寿财登录方式的openAPI，国寿财通过调用该openAPI来向sugar中添加和删除用户
  router.post('/openapi/gpic/addUsers', wrapAsync(router.action('openapi/gpic').addUsers));
  router.post('/openapi/gpic/deleteUsers', wrapAsync(router.action('openapi/gpic').deleteUsers));
  router.get('/openapi/gpic/getAllUser', wrapAsync(router.action('openapi/gpic').getAllUser));

  // 邮储登录方式openAPI
  if (yog.conf.sugar.login.type === 'psbc') {
    router.get('/sugar/psbc/group', wrapAsync(router.action('psbc').group));
    if (yog.conf.sugar.sugarConfig.enableDorisAE) {
      router.get('/api/dae/group/:groupHash/queue', wrapAsync(router.action('psbc').getQueueList));
      router.get('/sugar/psbc/database', wrapAsync(router.action('psbc').database));
      router.post('/api/psbc/group/:groupHash/database/add', wrapAsync(router.action('psbc').addDatabase));
      router.post('/api/psbc/group/:groupHash/database/connectTest', wrapAsync(router.action('psbc').connectTest));
      router.post('/api/psbc/group/:groupHash/database/:databaseHash', wrapAsync(router.action('psbc').editDatabase));
    }
  }

  /* 报表页面编辑锁 lock 相关 */
  router.get('/api/report/:reportHash/lock/access', wrapAsync(router.action('report-lock').access));
  router.post('/api/report/:reportHash/lock', wrapAsync(router.action('report-lock').lock));
  router.post('/api/report/:reportHash/unlock', wrapAsync(router.action('report-lock').unlock));
  router.post('/api/report/:reportHash/lock/sync', wrapAsync(router.action('report-lock').sync));
  router.post('/api/report/:reportHash/lock/isMy', wrapAsync(router.action('report-lock').isMy));

  /* 数据填报 编辑锁 lock 相关 */
  router.get('/api/dataSubmit/:dataSubmitHash/lock/access', wrapAsync(router.action('data-submit-lock').access));
  router.post('/api/dataSubmit/:dataSubmitHash/lock', wrapAsync(router.action('data-submit-lock').lock));
  router.post('/api/dataSubmit/:dataSubmitHash/unlock', wrapAsync(router.action('data-submit-lock').unlock));
  router.post('/api/dataSubmit/:dataSubmitHash/lock/sync', wrapAsync(router.action('data-submit-lock').sync));
  router.post('/api/dataSubmit/:dataSubmitHash/lock/isMy', wrapAsync(router.action('data-submit-lock').isMy));

  /* 数据门户 编辑锁 lock 相关 */
  router.get('/api/dataPortal/:dataPortalHash/lock/access', wrapAsync(router.action('data-portal-lock').access));
  router.post('/api/dataPortal/:dataPortalHash/lock', wrapAsync(router.action('data-portal-lock').lock));
  router.post('/api/dataPortal/:dataPortalHash/unlock', wrapAsync(router.action('data-portal-lock').unlock));
  router.post('/api/dataPortal/:dataPortalHash/lock/sync', wrapAsync(router.action('data-portal-lock').sync));
  router.post('/api/dataPortal/:dataPortalHash/lock/isMy', wrapAsync(router.action('data-portal-lock').isMy));

  /* Dashboard 页面编辑锁 lock 相关 */
  router.get('/api/dashboard/:dashboardHash/lock/access', wrapAsync(router.action('dashboard-lock').access));
  router.post('/api/dashboard/:dashboardHash/lock', wrapAsync(router.action('dashboard-lock').lock));
  router.post('/api/dashboard/:dashboardHash/unlock', wrapAsync(router.action('dashboard-lock').unlock));
  router.post('/api/dashboard/:dashboardHash/lock/sync', wrapAsync(router.action('dashboard-lock').sync));
  router.post('/api/dashboard/:dashboardHash/lock/isMy', wrapAsync(router.action('dashboard-lock').isMy));

  /* 数据模型 编辑锁 lock 相关 */
  router.get('/api/dataModel/:dataModelHash/lock/access', wrapAsync(router.action('data-model-lock').access));
  router.post('/api/dataModel/:dataModelHash/lock', wrapAsync(router.action('data-model-lock').lock));
  router.post('/api/dataModel/:dataModelHash/unlock', wrapAsync(router.action('data-model-lock').unlock));
  router.post('/api/dataModel/:dataModelHash/lock/sync', wrapAsync(router.action('data-model-lock').sync));
  router.post('/api/dataModel/:dataModelHash/lock/isMy', wrapAsync(router.action('data-model-lock').isMy));

  /* 训练编辑 编辑锁 lock 相关 */
  router.get('/api/train/:trainHash/lock/access', wrapAsync(router.action('ml-train-lock').access));
  router.post('/api/train/:trainHash/lock', wrapAsync(router.action('ml-train-lock').lock));
  router.post('/api/train/:trainHash/unlock', wrapAsync(router.action('ml-train-lock').unlock));
  router.post('/api/train/:trainHash/lock/sync', wrapAsync(router.action('ml-train-lock').sync));
  router.post('/api/train/:trainHash/lock/isMy', wrapAsync(router.action('ml-train-lock').isMy));

  /* 场景编辑器 编辑锁 lock 相关 */
  router.get('/api/scene/:sceneHash/lock/access', wrapAsync(router.action('scene-lock').access));
  router.post('/api/scene/:sceneHash/lock', wrapAsync(router.action('scene-lock').lock));
  router.post('/api/scene/:sceneHash/unlock', wrapAsync(router.action('scene-lock').unlock));
  router.post('/api/scene/:sceneHash/lock/sync', wrapAsync(router.action('scene-lock').sync));
  router.post('/api/scene/:sceneHash/lock/isMy', wrapAsync(router.action('scene-lock').isMy));

  /* 自助取数 编辑锁 lock 相关 */
  router.get(
    '/api/dataEasyFetch/:dataEasyFetchHash/lock/access',
    wrapAsync(router.action('data-easy-fetch-lock').access)
  );
  router.post('/api/dataEasyFetch/:dataEasyFetchHash/lock', wrapAsync(router.action('data-easy-fetch-lock').lock));
  router.post('/api/dataEasyFetch/:dataEasyFetchHash/unlock', wrapAsync(router.action('data-easy-fetch-lock').unlock));
  router.post('/api/dataEasyFetch/:dataEasyFetchHash/lock/sync', wrapAsync(router.action('data-easy-fetch-lock').sync));
  router.post('/api/dataEasyFetch/:dataEasyFetchHash/lock/isMy', wrapAsync(router.action('data-easy-fetch-lock').isMy));

  /* 页面普通API */
  registerAction('group');
  registerAction(
    'user-demo',
    '/api/userDemo',
    {install: 'post'},
    {
      getDemo: true,
      install: true
    }
  );

  // /api/demo不需要校验登录
  router.get('/api/demo/templates', wrapAsync(router.action('user-demo').templates));

  registerAction('news', '/api/news');
  registerAction('overview', '/api/overview');
  registerAction('workbench', '/api/group/:groupHash/workbench');
  registerAction(
    'report',
    '/api/group/:groupHash/report',
    {},
    {
      getReportWithChartsAndConditions: true
    }
  );
  registerAction('explore', '/api/group/:groupHash/explore', {
    AIExploreDetail: 'get'
  });
  router.get('/api/group/:groupHash/explore/:exploreHash', wrapAsync(router.action('explore').AIExploreDetail));
  registerAction(
    'analyze',
    '/api/group/:groupHash/analyze',
    {
      getAnalyze: 'post'
    },
    {
      getAnalyze: true
    }
  );
  registerAction(
    'undulate',
    '/api/group/:groupHash/undulate',
    {
      calcUndulate: 'post'
    },
    {
      calcUndulate: true
    }
  );
  registerAction(
    'data-submit',
    '/api/group/:groupHash/dataSubmit',
    {
      importData: 'post',
      exportData: 'post'
    },
    {
      getTableSchema: true,
      getDataSubmit: true,
      importData: true,
      exportData: true
    }
  );
  registerAction(
    'data-portal',
    '/api/group/:groupHash/dataPortal',
    {},
    {
      getDataPortal: true
    }
  );
  registerAction(
    'dashboard',
    '/api/group/:groupHash/dashboard',
    {},
    {
      getDashboardWithChartsAndConditions: true
    }
  );
  registerAction('data-kinships', '/api/group/:groupHash/dataKinships', {
    getDataKinships: 'post'
  });

  registerAction('authority-audit', '/api/group/:groupHash/authorityAudit', {
    getAuthorityUsersAndRoles: 'post'
  });

  router.get('/api/notice', wrapAsync(router.action('notice').get));
  router.get('/api/weather', wrapAsync(router.action('weather').getForecasts));
  router.post('/api/i18n/:lang', wrapAsync(router.action('i18n').post));

  // router.get('/api/group/:groupHash/statistics', wrapAsync(router.action('statistics').getList));
  registerAction('statistics', '/api/group/:groupHash/statistics', {
    getDetail: 'post'
  });

  registerAction('statistics-resource', '/api/group/:groupHash/statistics/resource');
  router.post('/api/group/:groupHash/statistics/overView', wrapAsync(router.action('statistics').overView));
  registerAction('manage/statistics', '/api/manage/company/:companyHash/statistics', {
    overView: 'post',
    getDetail: 'post'
  });
  registerAction('scene', '/api/group/:groupHash/scene', {}, {getScene: true});

  // 大屏编辑器用的 api，不带 groupHash
  router.get('/api/sceneShare/:sceneHash', wrapAsync(router.action('scene').getShareScene));
  // 场景预览及编辑里用来获取数据的 api
  router.post(
    '/api/scene/:sceneHash/scene-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyScene)
  );

  // 前后端版本一致性验证
  router.get('/api/saveData', wrapAsync(router.action('manage/company').saveData));
  router.get('/api/dashboardShare/:dashboardShareToken/saveData', wrapAsync(router.action('manage/company').saveData));
  router.get(
    '/api/reportShare/:reportShareToken/report/:reportHash/saveData',
    wrapAsync(router.action('manage/company').saveData)
  );

  // 大屏分享之后访问相关的api
  router.get(
    '/api/dashboardShare/:dashboardShareToken/getDashboardShare',
    wrapAsync(router.action('dashboard').getDashboardShare)
  );

  router.post(
    '/api/dashboardShare/:dashboardShareToken/getShareSceneData',
    wrapAsync(router.action('chart-data').getShareSceneData)
  );
  router.post(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/chart-data/:chartHash',
    wrapAsync(router.action('chart-data').getShareChartData)
  );
  router.post(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/condition-data/:conditionHash',
    wrapAsync(router.action('condition-data').getShareConditionData)
  );
  router.post(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/chart-data/:chartHash/drillDown',
    wrapAsync(router.action('chart-data').drillDown)
  );
  router.get(
    '/api/dashboardShare/:dashboardShareToken/:pageType/:dashboardHash/chartComment/:chartFrontendId',
    wrapAsync(router.action('manage/chart-comment').getShareComment)
  );

  // education 活动的公开api
  router.post('/api/education/:companyHash', wrapAsync(router.action('education-inviter').recordInviter));

  // 数据填报发布和insertData
  router.get(
    '/api/dataSubmitPublish/:dataSubmitHash/:dataSubmitToken/getDataSubmitPublish',
    wrapAsync(router.action('data-submit').getDataSubmitPublish)
  );
  // 插入数据不校验 token(预览也可以提交数据)
  router.post('/api/dataSubmitPublish/:dataSubmitHash/insert', wrapAsync(router.action('data-submit').insert));
  router.post(
    '/api/dataSubmitPublish/:dataSubmitToken/dataSubmit/:dataSubmitHash/form-data/:formHash',
    wrapAsync(router.action('form-data').getPublishFormData)
  );

  // 报表分享之后以及定时推送截图时访问相关的api
  router.get(
    '/api/reportShare/:reportHash/:reportShareToken/getReportShare',
    wrapAsync(router.action('report').getReportShare)
  );
  router.post(
    '/api/reportShare/:reportShareToken/report/:reportHash/chart-data/:chartHash',
    wrapAsync(router.action('chart-data').getShareChartData)
  );
  router.post(
    '/api/reportShare/:reportShareToken/report/:reportHash/condition-data/:conditionHash',
    wrapAsync(router.action('condition-data').getShareConditionData)
  );
  router.post(
    '/api/reportShare/:reportShareToken/report/:reportHash/chart-data/:chartHash/drillDown',
    wrapAsync(router.action('chart-data').drillDown)
  );
  router.post(
    '/api/reportShare/:reportShareToken/report/:reportHash/chart-condition-data',
    wrapAsync(router.action('chart-condition-data').getShareChartConditionData)
  );
  router.get(
    '/api/reportShare/:reportShareToken/:pageType/:reportHash/chartComment/:chartFrontendId',
    wrapAsync(router.action('manage/chart-comment').getShareComment)
  );

  // 智能问数页分享相关 api
  router.get(
    '/api/ernieShare/:ernieHash/:ernieShareToken/getErnieShare',
    wrapAsync(router.action('explore').getErnieShare)
  );
  router.get(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/getErnieDataModels',
    wrapAsync(router.action('data-model').treeListShareErnie)
  );
  router.get(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/getDataModel/:dataModelHash',
    wrapAsync(router.action('data-model').shareShow)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/chartResultMessage',
    wrapAsync(router.action('ernie-ask/ernie-ask-chart-result').postShare)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/conclusionMessage',
    wrapAsync(router.action('ernie-ask/ernie-ask-conclusion').postShare)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/getAppropriateDataModel',
    wrapAsync(router.action('ernie-ask/ernie-ask-switch-data-model').getAppropriateDataModelShare)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyShare)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/feedback',
    wrapAsync(router.action('ernie-ask/feedback').feedbackShare)
  );
  router.post(
    '/api/ernieShare/:ernieShareToken/ernie/:ernieHash/getSelectDimensionsValue',
    wrapAsync(router.action('ernie-ask/ernie-ask-chart-result').getSelectDimensionsValue)
  );

  // 探索页分享相关 api
  router.get(
    '/api/reportShare/:reportShareToken/explore/:reportHash/getExploreDataModels',
    wrapAsync(router.action('data-model').treeListShare)
  );
  router.get(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/getExploreDataModels',
    wrapAsync(router.action('data-model').treeListShare)
  );
  router.get(
    '/api/reportShare/:reportShareToken/explore/:reportHash/getDataModel/:dataModelHash',
    wrapAsync(router.action('data-model').shareShow)
  );
  router.get(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/getDataModel/:dataModelHash',
    wrapAsync(router.action('data-model').shareShow)
  );
  router.post(
    '/api/reportShare/:reportShareToken/explore/:reportHash/unit/query',
    wrapAsync(router.action('nlp-unit').shareQuery)
  );
  router.post(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/unit/query',
    wrapAsync(router.action('nlp-unit').shareQuery)
  );
  router.post(
    '/api/reportShare/:reportShareToken/explore/:reportHash/unit/audio',
    wrapAsync(router.action('nlp-unit').shareAudio)
  );
  router.post(
    '/api/dashboardShare/:dashboardShareToken/dashboard/:dashboardHash/unit/audio',
    wrapAsync(router.action('nlp-unit').shareAudio)
  );
  router.post(
    '/api/reportShare/:reportShareToken/explore/:reportHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyShare)
  );

  // 自动分析页相关 api
  router.post(
    '/api/analyze/:dataModelHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyAnalyze)
  );
  router.post(
    '/api/analyze/:dataModelHash/condition-data/dataModelProxy',
    wrapAsync(router.action('condition-data').dataModelProxyAnalyze)
  );
  router.post(
    '/api/analyze/:dataModelHash/condition-data/staticProxy',
    wrapAsync(router.action('condition-data').staticProxyAnalyze)
  );

  registerAction(
    'self-report',
    '/api/self/report',
    {
      multiDelete: 'put',
      updateOrder: 'put',
      getFormedTree: 'post',
      multiMove: 'put'
    },
    {
      getReportWithChartsAndConditions: true
    }
  );
  registerAction(
    'self-dashboard',
    '/api/self/dashboard',
    {
      multiDelete: 'put',
      updateOrder: 'put'
    },
    {
      getDashboardWithChartsAndConditions: true
    }
  );
  registerAction('user', '/api/user', {
    sendChangeEmailCode: 'put',
    changeEmail: 'put',
    changePassword: 'put',
    all: 'get'
  });
  // registerAction('self-account', '/api/self/account');
  registerAction('self-company', '/api/self/company');
  registerAction(
    'database',
    '/api/group/:groupHash/database',
    {
      getTableData: 'post',
      getTableDataSchema: 'post',
      runSQL: 'post',
      quickSaveTableData: 'post',
      bulkDeleteTableData: 'post',
      insertTableData: 'post',
      truncateTable: 'post',
      changeTableData: 'put'
    },
    {
      canEditSimpleList: true,
      getTableList: true,
      getTableData: true,
      getTableDataSchema: true,
      runSQL: true,
      quickSaveTableData: true,
      bulkDeleteTableData: true,
      truncateTable: true,
      insertTableData: true,
      changeTableData: true,
      getTableSchema: true,
      getDatabaseList: true
    }
  );
  registerAction('sql-model', '/api/group/:groupHash/sqlModel');
  registerAction('custom-table', '/api/group/:groupHash/customTable');
  registerAction('chart-warning', '/api/group/:groupHash/chartWarning', {
    userVerificationOfInfoflow: 'post'
  });
  registerAction('rename-lib', '/api/group/:groupHash/renameLib');
  registerAction(
    'data-model',
    '/api/group/:groupHash/dataModel',
    {
      syncStructure: 'post',
      getViewDataPartially: 'post',
      getSelectDimensionsValue: 'post'
    },
    {
      syncStructure: true,
      getViewData: true,
      getViewDataPartially: true,
      getFieldStatistic: true,
      getFields: true,
      getModelDimsDistinctData: true
    }
  );
  registerAction('api-adaptor', '/api/group/:groupHash/apiAdaptor');

  registerAction('json', '/api/group/:groupHash/json');
  registerAction(
    'html',
    '/api/group/:groupHash/html',
    {},
    {
      copy: true
    }
  );
  router.get('/openapi/custom-iframe/:htmlHash', wrapAsync(router.action('html').show));

  // geoJSON相关服务
  registerAction('geo-json', '/api/group/:groupHash/geoJson/:hash', {
    deleteFile: 'delete'
  });
  router.get('/openapi/map/:hash', wrapAsync(router.action('geo-json').get));

  // 定时推送相关接口
  registerAction('manage/email', '/api/manage/group/:groupHash/email', {
    manualTrigger: 'post',
    userVerificationOfInfoflow: 'post'
  });

  // 自助取数相关接口
  registerAction(
    'manage/data-easy-fetch',
    '/api/manage/group/:groupHash/dataEasyFetch',
    {
      download: 'get',
      retry: 'post',
      stop: 'post'
    },
    {
      download: true,
      retry: true,
      stop: true
    }
  );
  registerAction(
    'data-easy-fetch',
    '/api/group/:groupHash/dataEasyFetch',
    {
      canPostDataEasyFetch: 'post',
      canUsedDataModels: 'post',
      dataEasyFetchCanUseDataModel: 'post'
    },
    {getDataEasyFetch: true}
  );
  // 获取自助取数数据
  router.post(
    '/api/dataEasyFetch/:dataEasyFetchHash/chart-data/dataModelProxy',
    wrapAsync(router.action('data-easy-fetch').dataModelProxy)
  );

  // 数据服务相关接口
  registerAction(
    'manage/data-service',
    '/api/manage/group/:groupHash/dataService',
    {
      accessKey: 'post'
    },
    {}
  );
  router.put(
    '/api/manage/group/:groupHash/dataService/accessKey/:accessKeyHash',
    wrapAsync(router.action('manage/data-service').editAccessKey)
  );
  router.delete(
    '/api/manage/group/:groupHash/dataService/accessKey/:accessKeyHash',
    wrapAsync(router.action('manage/data-service').removeAccessKey)
  );
  registerAction('manage/img-service', '/api/manage/group/:groupHash/imgService');

  // LLMJSON和图表JSON互转 API
  router.post('/api/gbi/json2chart', wrapAsync(router.action('gbi/json-chart-transfer').json2chart));
  router.post('/api/gbi/chart2json', wrapAsync(router.action('gbi/json-chart-transfer').chart2json));

  // 根据取数文件id，获取文件名称、维度、度量
  router.post('/api/gbi/fileInfo', wrapAsync(router.action('gbi/retrieve-files').fileInfo));
  // 获取学习模型的预览数据
  router.post('/api/gbi/dmlearn/viewData', wrapAsync(router.action('gbi/retrieve-files').dmLearnViewData));

  // 获取和设置提示标签
  router.post('/api/gbi/tips/status', wrapAsync(router.action('gbi/tips').post));
  registerAction('gbi/user-config', '/api/gbi/user/clarification/config');

  // 获取数据填报表单的数据
  router.post('/api/dataSubmit/:dataSubmitHash/form-data/apiProxy', wrapAsync(router.action('form-data').apiProxy));
  router.post('/api/dataSubmit/:dataSubmitHash/form-data/sqlProxy', wrapAsync(router.action('form-data').sqlProxy));
  router.post(
    '/api/dataSubmit/:dataSubmitHash/form-data/staticProxy',
    wrapAsync(router.action('form-data').staticProxy)
  );
  router.post(
    '/api/dataSubmit/:dataSubmitHash/form-data/dataModelProxy',
    wrapAsync(router.action('form-data').dataModelProxy)
  );
  router.post('/api/dataSubmit/:dataSubmitHash/form-data/:formHash', wrapAsync(router.action('form-data').get));

  // 获取过滤条件的数据
  router.post('/api/report/:reportHash/condition-data/apiProxy', wrapAsync(router.action('condition-data').apiProxy));
  router.post('/api/report/:reportHash/condition-data/sqlProxy', wrapAsync(router.action('condition-data').sqlProxy));
  router.post(
    '/api/report/:reportHash/condition-data/staticProxy',
    wrapAsync(router.action('condition-data').staticProxy)
  );
  router.post(
    '/api/report/:reportHash/condition-data/dataModelProxy',
    wrapAsync(router.action('condition-data').dataModelProxy)
  );
  router.post('/api/report/:reportHash/condition-data/:conditionHash', wrapAsync(router.action('condition-data').get));

  // 获取图表的数据
  router.post(
    '/api/report/:reportHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxy)
  );

  router.post(
    '/api/AIExplore/:exploreHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxy)
  );
  router.post(
    '/api/report/:reportHash/chart-data/dataModelProxyDataset',
    wrapAsync(router.action('chart-data').dataModelProxyDataset)
  );
  router.post(
    '/api/dashboard/:dashboardHash/chart-data/dataModelProxyDataset',
    wrapAsync(router.action('chart-data').dataModelProxyDataset)
  );
  router.post('/api/report/:reportHash/chart-data/apiProxy', wrapAsync(router.action('chart-data').apiProxy));
  router.post('/api/report/:reportHash/chart-data/sqlProxy', wrapAsync(router.action('chart-data').sqlProxy));
  router.post('/api/report/:reportHash/chart-data/jsonProxy', wrapAsync(router.action('chart-data').jsonProxy));
  router.post('/api/report/:reportHash/chart-data/staticProxy', wrapAsync(router.action('chart-data').staticProxy));
  router.post('/api/report/:reportHash/chart-data/:chartHash', wrapAsync(router.action('chart-data').get));
  router.post(
    '/api/report/:reportHash/chart-data/:chartHash/drillDown',
    wrapAsync(router.action('chart-data').drillDown)
  );

  router.post(
    '/api/explore/:reportExploreHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyExplore)
  );
  // 获取图表级过滤条件的数据
  router.post(
    '/api/chart-condition-data/dataModelProxy',
    wrapAsync(router.action('chart-condition-data').dataModelProxyChartCondition)
  );

  // 获取过滤条件的数据
  router.post(
    '/api/dashboard/:dashboardHash/condition-data/apiProxy',
    wrapAsync(router.action('condition-data').apiProxy)
  );
  router.post(
    '/api/dashboard/:dashboardHash/condition-data/sqlProxy',
    wrapAsync(router.action('condition-data').sqlProxy)
  );
  router.post(
    '/api/dashboard/:dashboardHash/condition-data/staticProxy',
    wrapAsync(router.action('condition-data').staticProxy)
  );
  router.post(
    '/api/dashboard/:dashboardHash/condition-data/dataModelProxy',
    wrapAsync(router.action('condition-data').dataModelProxy)
  );
  router.post(
    '/api/dashboard/:dashboardHash/condition-data/:conditionHash',
    wrapAsync(router.action('condition-data').get)
  );
  // 获取 Dashboard 图表的数据
  router.post(
    '/api/dashboard/:dashboardHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxy)
  );
  router.post('/api/dashboard/:dashboardHash/chart-data/apiProxy', wrapAsync(router.action('chart-data').apiProxy));
  router.post('/api/dashboard/:dashboardHash/chart-data/sqlProxy', wrapAsync(router.action('chart-data').sqlProxy));
  router.post('/api/dashboard/:dashboardHash/chart-data/jsonProxy', wrapAsync(router.action('chart-data').jsonProxy));
  router.post(
    '/api/dashboard/:dashboardHash/chart-data/staticProxy',
    wrapAsync(router.action('chart-data').staticProxy)
  );
  router.post('/api/dashboard/:dashboardHash/chart-data/:chartHash', wrapAsync(router.action('chart-data').get));
  router.post(
    '/api/dashboard/:dashboardHash/chart-data/:chartHash/drillDown',
    wrapAsync(router.action('chart-data').drillDown)
  );

  // gbi对话相关接口
  router.post(
    '/api/conversation/:groupHash/session/add',
    wrapAsync(router.action('conversation/session').createSession)
  );

  router.post('/api/conversation/:groupHash/message/add', wrapAsync(router.action('conversation/query').add));

  router.post(
    '/api/conversation/:groupHash/message/:sessionHash',
    wrapAsync(router.action('conversation/message').getMessageList)
  );

  // 智能问数相关api
  router.post('/api/gbi/space/list', wrapAsync(router.action('gbi/workspace').list));
  router.post('/api/gbi/space/add', wrapAsync(router.action('gbi/workspace').createSpace));
  router.post('/api/gbi/space/init', wrapAsync(router.action('gbi/workspace').init));
  router.post('/api/gbi/space/edit', wrapAsync(router.action('gbi/workspace').editSpace));
  router.post('/api/gbi/space/history', wrapAsync(router.action('gbi/workspace').history));
  router.post('/api/gbi/space/remove', wrapAsync(router.action('gbi/workspace').removeSpace));
  router.post('/api/gbi/session/add', wrapAsync(router.action('gbi/session').createSession));
  router.post('/api/gbi/session/message/add', wrapAsync(router.action('gbi/message').add));
  router.post('/api/gbi/session/message/list', wrapAsync(router.action('gbi/message').list));
  router.post('/api/gbi/gbi-data/query', wrapAsync(router.action('gbi/table-query').query));
  router.post(
    '/api/gbi/:dataModelHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyGBI)
  );
  router.post('/api/gbi/dataModel', wrapAsync(router.action('data-model').getSelectDimensionsValueGBI));

  // 获取数据模型是否有问数权限
  router.post('/api/gbi/dataModels/auth', wrapAsync(router.action('gbi-dm-learn').dmLearnDataModelAuth));
  router.post('/api/gbi-iframe/dataModels/auth', wrapAsync(router.action('gbi-dm-learn').dmLearnDataModelAuth));

  // 查询修改参数后的归因结果
  router.post('/api/gbi/attribution/result', wrapAsync(router.action('gbi/json-chart-transfer').attributionResult));
  router.post(
    '/api/gbi-iframe/attribution/result',
    wrapAsync(router.action('gbi/json-chart-transfer').attributionResult)
  );

  // 获取指令模版
  router.post('/api/gbi-consume/command/template', wrapAsync(router.action('gbi/command-template').post));

  //报表消费相关api
  router.post('/api/gbi-consume/session/add', wrapAsync(router.action('consume/session').createSession));
  router.post('/api/gbi-consume/session/init', wrapAsync(router.action('consume/session').init));
  router.post('/api/gbi-consume/session/history', wrapAsync(router.action('consume/session').history));
  router.post('/api/gbi-consume/session/ignore', wrapAsync(router.action('consume/session').ignore));
  router.post('/api/gbi-consume/message/add', wrapAsync(router.action('consume/message').add));
  router.post('/api/gbi-consume/message/list', wrapAsync(router.action('consume/message').list));
  router.post(
    '/api/gbi-consume/:reportHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyConsume)
  );
  router.post('/api/gbi-consume/dataModel', wrapAsync(router.action('data-model').getSelectDimensionsValueConsume));

  // 重新生成总结相关接口
  router.post('/api/gbi-consume/:pageHash/regenerate-summary', wrapAsync(router.action('gbi-consume/regenerate-summary').regenerateSummary));
  router.get('/api/gbi-consume/:pageHash/query-summary', wrapAsync(router.action('gbi-consume/regenerate-summary').querySummaryContent));

  // 文心一言相关接口
  // router.post('/api/group/:groupHash/ernie/chartMessage', wrapAsync(router.action('ernie').post));
  router.post(
    '/api/group/:groupHash/ernie/chartResultMessage',
    wrapAsync(router.action('ernie-ask/ernie-ask-chart-result').post)
  );
  router.post(
    '/api/group/:groupHash/ernie/conclusionMessage',
    wrapAsync(router.action('ernie-ask/ernie-ask-conclusion').post)
  );
  router.post(
    '/api/group/:groupHash/ernie/getAppropriateDataModel',
    wrapAsync(router.action('ernie-ask/ernie-ask-switch-data-model').getAppropriateDataModel)
  );
  router.post(
    '/api/group/:groupHash/ernie/:ernieHash/feedback',
    wrapAsync(router.action('ernie-ask/feedback').feedback)
  );

  router.post('/api/gbi/message', wrapAsync(router.action('gbi').post));
  router.post('/api/gbi/dataModel/info', wrapAsync(router.action('data-model').getModelSchemaByHash));

  // 智能问数运营管理
  router.get('/api/gbi/likeDislike/config', wrapAsync(router.action('gbi/gbi-like-dislike').get));
  router.post('/api/gbi/likeDislike/config', wrapAsync(router.action('gbi/gbi-like-dislike').post));
  router.post('/api/gbi/likeDislike/save', wrapAsync(router.action('gbi/gbi-like-dislike').saveLikeDislike));
  // iframe版本的likeDislike接口
  router.post('/api/gbi-iframe/likeDislike/config', wrapAsync(router.action('gbi/gbi-like-dislike').get));
  router.post('/api/gbi-iframe/likeDislike/save', wrapAsync(router.action('gbi/gbi-like-dislike').saveLikeDislike));
  router.post('/api/gbi/log', wrapAsync(router.action('gbi/access-log').post));
  router.post('/api/gbi-iframe/log', wrapAsync(router.action('gbi/access-log').post));
  router.get('/api/gbi/statistics/iframe', wrapAsync(router.action('gbi/statistics').getAllIframe));
  router.post('/api/gbi/statistics', wrapAsync(router.action('gbi/statistics').statistics));
  router.post('/api/gbi/statistics/query', wrapAsync(router.action('gbi/statistics').statisticsAllQuery));
  router.post('/api/gbi/statistics/export', wrapAsync(router.action('gbi/statistics').exportTable));

  // gbi-iframe相关接口
  router.post('/api/gbi-iframe/space/list', wrapAsync(router.action('gbi-iframe/workspace').list));
  router.post('/api/gbi-iframe/space/add', wrapAsync(router.action('gbi-iframe/workspace').createSpace));
  router.post('/api/gbi-iframe/space/init', wrapAsync(router.action('gbi-iframe/workspace').init));
  router.post('/api/gbi-iframe/space/edit', wrapAsync(router.action('gbi-iframe/workspace').editSpace));
  router.post('/api/gbi-iframe/space/history', wrapAsync(router.action('gbi-iframe/workspace').history));
  router.post('/api/gbi-iframe/space/remove', wrapAsync(router.action('gbi-iframe/workspace').removeSpace));
  router.post('/api/gbi-iframe/session/add', wrapAsync(router.action('gbi-iframe/session').createSession));
  router.post('/api/gbi-iframe/session/message/add', wrapAsync(router.action('gbi-iframe/message').add));
  router.post('/api/gbi-iframe/session/message/list', wrapAsync(router.action('gbi-iframe/message').list));
  router.post(
    '/api/gbi-iframe/config/setClarifySwitch',
    wrapAsync(router.action('gbi-iframe/workspace').setClarifySwitch)
  );
  router.post(
    '/api/gbi-iframe/config/getClarifySwitch',
    wrapAsync(router.action('gbi-iframe/workspace').getClarifySwitch)
  );
  router.post(
    '/api/gbi-iframe/:dataModelHash/chart-data/dataModelProxy',
    wrapAsync(router.action('chart-data').dataModelProxyGBIIframe)
  );
  router.post('/api/gbi-iframe/dataModel', wrapAsync(router.action('data-model').getSelectDimensionsValueGBIIframe));
  registerAction('gbi-iframe/iframe', '/api/gbi-iframe-manage/iframe', {
    baseInfo: 'post',
    list: 'get',
    del: 'post',
    detail: 'get',
    release: 'post',
    edit: 'post'
  });
  // 数据模型范围列表
  router.get('/api/gbi-iframe-manage/model/scope', wrapAsync(router.action('gbi-iframe/data-models').scope));
  // 获取系统陈澄清开关系统配置
  router.get('/api/gbi/system/config', wrapAsync(router.action('gbi-iframe/iframe').getSystemConfig));

  // iframe详情
  router.post('/api/gbi-iframe/iframe/detail', wrapAsync(router.action('gbi-iframe/iframe').detail));

  // 数据模型详情
  router.post('/api/gbi-iframe/dmlearn/viewData', wrapAsync(router.action('gbi-iframe/data-models').viewData));
  // 全部可用的模型范围
  router.post('/api/gbi-iframe/aiAsk/dataModels', wrapAsync(router.action('gbi-iframe/data-models').dataModels));
  // 模型详情信息
  router.post('/api/gbi-iframe/aiAsk/dataModelInfo', wrapAsync(router.action('gbi-iframe/data-models').dataModelInfo));
  router.post(
    '/api/gbi-iframe/dataModel/info',
    wrapAsync(router.action('gbi-iframe/data-models').getModelSchemaByHash)
  );

  // iclhub相关接口 
  // 页面修改度量点击http://localhost:8085/group/szjlj/manage/dataModel/edit/mld_ada15-9flsjbd-68xq3g3?__scp__=scp_ada15-552n7tqk-p284kv的保存之后，
  registerAction('iclhub', '/api/iclhub/template', {
    list: 'post',
    del: 'post',
    info: 'post',
    save: 'post'
  });
  router.post('/api/iclhub/template/generateLLM', wrapAsync(router.action('gbi-dm-learn').generateLLM));
  router.post('/api/gbi-iframe/iclhub/template/generateLLM', wrapAsync(router.action('gbi-dm-learn').generateLLM));

  // demo api，同时支持get和post
  router.get('/openapi/demo/form', wrapAsync(router.action('openapi/demo').form));
  router.post('/openapi/demo/form', wrapAsync(router.action('openapi/demo').form));
  router.get('/openapi/demo/condition', wrapAsync(router.action('openapi/demo').condition));
  router.post('/openapi/demo/condition', wrapAsync(router.action('openapi/demo').condition));
  router.get('/openapi/demo/chart', wrapAsync(router.action('openapi/demo').chart));
  router.post('/openapi/demo/chart', wrapAsync(router.action('openapi/demo').chart));
  router.get('/openapi/demo/empty', wrapAsync(router.action('openapi/demo').emptyGet));
  router.post('/openapi/demo/empty', wrapAsync(router.action('openapi/demo').emptyPost));

  registerAction('openapi/weather', '/openapi/weather');
  router.post(
    '/openapi/internal/getDataByJSON',
    wrapAsync(router.action('openapi/internal/data-model-service').getDataByJSON)
  );
  router.post(
    '/openapi/internal/session/model/schema',
    wrapAsync(router.action('openapi/internal/data-model-service').getModelSchemaInfoBySessionId)
  );
  router.post(
    '/openapi/internal/model/schema',
    wrapAsync(router.action('openapi/internal/data-model-service').getModelSchemaInfo)
  );
  router.get(
    '/openapi/internal/user/clarification/config',
    wrapAsync(router.action('openapi/internal/user-config').getConfigInfo)
  );
  registerAction('openapi/internal/page-config', '/openapi/internal/page/config', {
    getClarify: 'post',
    getMultiwheel: 'post'
  });

  // openapi根据多个数据模型hash获取对应空间下的所有知识
  registerAction('openapi/knowledge', '/openapi/internal/knowledge');

  registerAction('gbi-dm-learn', '/api/gbi-consume/gbiDMLearn', {
    status: 'post',
    generateLLM: 'post'
  });

  router.post('/openapi/internal/llm2data', wrapAsync(router.action('gbi-dm-learn').llm2data));
  router.post('/openapi/internal/dataModelIndexJob', wrapAsync(router.action('gbi-dm-learn').dataModelIndexJob));

  // 手动添加定时统计任务
  router.get(
    '/openapi/internal/gbi/statistics',
    wrapAsync(router.action('openapi/internal/gbi-statistics').addGBIStatistics)
  );

  // geojson openapi
  router.get('/openapi/geo/json', wrapAsync(router.action('openapi/geo').json));
  router.get('/openapi/geo/allProvince', wrapAsync(router.action('openapi/geo').allProvince));
  router.get('/openapi/geo/allCity', wrapAsync(router.action('openapi/geo').allCity));
  router.get('/openapi/geo/getCityAdcord', wrapAsync(router.action('openapi/geo').getCityAdcord));
  // 密码登录模式下，用来创建第一个用户
  router.get('/openapi/createUser', wrapAsync(router.action('openapi/create-user').create));
  // 获取微信 sdk 所需的鉴权信息
  router.get('/openapi/getWXInfo', wrapAsync(router.action('openapi/wechat').getWXInfo));
  // 更新用户可安装 demo
  router.get('/openapi/updateDemos', wrapAsync(router.action('openapi/update-demo').updateDemos));

  // 预测
  router.post('/openapi/forecast', wrapAsync(router.action('forecast').post));

  //数据模型
  router.get('/openapi/getDataModelFieldsInfo', wrapAsync(router.action('openapi/datamodel').getDataModelFieldsInfo));

  // 数据源连通性测试
  router.post('/openapi/testSingleDatabase', wrapAsync(router.action('openapi/database').testSingleDatabase));
  router.get('/openapi/testAllDatabase', wrapAsync(router.action('openapi/database').testAllDatabase));

  // OPENAPI相关
  registerAction('openapi/v1/database', '/openapi/v1/group/:groupKey/database');
  registerAction('openapi/v1/data-model', '/openapi/v1/group/:groupKey/datamodel');
  registerAction('openapi/v1/report', '/openapi/v1/group/:groupKey/report');
  registerAction('openapi/v1/user', '/openapi/v1/group/:groupKey/user', {
    addReport: 'post'
  });
  registerAction('openapi/v1/role', '/openapi/v1/group/:groupKey/role');
  registerAction('openapi/v1/company-user', '/openapi/v1/company/user');
  registerAction('openapi/v1/company-group', '/openapi/v1/company/group');
  registerAction('openapi/v1/mail-page', '/openapi/v1/group/:groupKey/mailpage');
  // OPENAPI相关--统计分析
  registerAction('openapi/v2/statistics', '/openapi/v2/group/:groupKey/statistics');
  registerAction('openapi/v2/company-statistics', '/openapi/v2/company/statistics');
  registerAction('openapi/v2/data-service', '/openapi/v2/group/:groupKey/dataService/:hash');
  registerAction('openapi/v2/img-service', '/openapi/v2/group/:groupKey/imgService/:hash');
  registerAction('openapi/v2/authority-audit', '/openapi/v2/group/:groupKey/authorityAudit');

  registerAction('openapi/v2/report', '/openapi/v2/group/:groupKey/report');
  registerAction('openapi/v2/dashboard', '/openapi/v2/group/:groupKey/dashboard');
  registerAction('openapi/v2/role', '/openapi/v2/group/:groupKey/role', {
    manualTrigger: 'post'
  });
  registerAction('openapi/v2/ernie-ask', '/openapi/v2/group/:groupKey/ernieAsk'); // 智能问数相关openApi

  // gbi智能问数openApi
  router.post('/openapi/v2/gbi/message/add', wrapAsync(router.action('openapi/v2/gbi/message').sendMessage));
  router.post('/openapi/v2/gbi/message/list', wrapAsync(router.action('openapi/v2/gbi/message').getMessageList));
  router.post('/openapi/v2/gbi/getDataByJson', wrapAsync(router.action('openapi/v2/gbi/message').getChartData));
  router.post('/openapi/v2/gbi/space/list', wrapAsync(router.action('openapi/v2/gbi/message').getSpaceList));
  router.post('/openapi/v2/gbi/space/history', wrapAsync(router.action('openapi/v2/gbi/message').history));
  router.post('/openapi/v2/gbi/echarts', wrapAsync(router.action('openapi/v2/gbi/echarts/index').llmJsonToChart));
  registerAction('openapi/v2/gbi/page-config', '/openapi/v2/gbi/config', {
    getClarify: 'post',
    setClarify: 'post',
    getMultiwheel: 'post',
    setMultiwheel: 'post'
  });
  registerAction('openapi/v2/gbi/data-model', '/openapi/v2/gbi/dataModel', {
    list: 'post',
    info: 'post',
    viewData: 'post'
  });
  registerAction('openapi/v2/gbi/workspace', '/openapi/v2/gbi/space', {
    add: 'post',
    edit: 'post',
    init: 'post',
    remove: 'post'
  });

  registerAction('openapi/test', '/openapi/test');

  if (yog.conf.sugar.smartAudio.enable) {
    registerAction('openapi/smart-audio', '/openapi/smartAudio', {
      sendOrder: 'post'
    });
  }

  // 浏览器前端通过请求该接口，来记录特定的日志信息
  router.post('/openapi/client-log', wrapAsync(router.action('openapi/client-log').log));
  router.options('/openapi/client-log', wrapAsync(router.action('openapi/client-log').preFlight));

  // nl2sql openAPI
  router.post('/openapi/nl2sql/getTable', wrapAsync(router.action('openapi/nl2sql').getTable));
  router.post('/openapi/nl2sql/getSql', wrapAsync(router.action('openapi/nl2sql').getSql));

  // 图片服务
  router.post('/api/image/upload', wrapAsync(router.action('image').post));
  router.get('/api/image/s/:companyId/:imageHash', wrapAsync(router.action('image').getSaaSImg));
  router.get('/api/image/s/:imageHash', wrapAsync(router.action('image').getSaaSImg));
  router.get('/api/image/p/:imageHash', wrapAsync(router.action('image').getPrivateImg));

  // UNIT
  router.post('/api/unit/query', wrapAsync(router.action('nlp-unit').query));
  router.post('/api/unit/audio', wrapAsync(router.action('nlp-unit').audio));
  // 企业微信回调
  registerAction('openapi/companywx', '/openapi/companywx');

  // 决策智能相关 API，以 /api/ml/ 开头
  registerAction(
    'manage/ml-train',
    '/api/ml/manage/group/:groupHash/train',
    {
      publishModel: 'post',
      testModel: 'post',
      updateOrder: 'put',
      updateFolder: 'put',
      getFormedTree: 'post',
      multiMove: 'put',
      multiDelete: 'put'
    },
    {
      publishModel: true,
      testModel: true
    }
  );
  registerAction(
    'manage/ml-dump',
    '/api/ml/manage/group/:groupHash/dump',
    {
      reRun: 'put',
      multiMove: 'put',
      multiDelete: 'put',
      updateOrder: 'put',
      updateFolder: 'put',
      getFormedTree: 'post',
      updateDumpOpen: 'post',
      writeGrantTest: 'post'
    },
    {
      updateDumpOpen: true
    }
  );
  registerAction(
    'manage/ml-model',
    '/api/ml/manage/group/:groupHash/model',
    {
      updateOrder: 'put',
      updateFolder: 'put',
      getFormedTree: 'post',
      multiMove: 'put',
      multiDelete: 'put',
      clearCache: 'post',
      testBMLService: 'post'
    },
    {
      clearCache: true,
      getAutoMLModelList: true,
      getMLModelPerformance: true,
      getMLModelPerformanceFromModel: true
    }
  );
  registerAction(
    'ml-train',
    '/api/ml/group/:groupHash/train',
    {},
    {
      getTrainWithSteps: true,
      reTrain: true,
      stopTrain: true
    }
  );
  registerAction('ml-model', '/api/ml/group/:groupHash/model');
  router.post('/api/file/upload', wrapAsync(router.action('file').post));

  // api not found
  router.all('/api/*', (req: Request, res: Response) => {
    if (req.xhr || (req.headers.accept && ~req.headers.accept.indexOf('json'))) {
      throw new NotFoundError('API Not Found');
    }
    return res.status(404).send('API Not Found');
  });

  // openapi not found
  router.all('/openapi/*', (req: Request, res: Response) => {
    if (req.xhr || (req.headers.accept && ~req.headers.accept.indexOf('json'))) {
      throw new NotFoundError('openAPI Not Found');
    }
    return res.status(404).send('openAPI Not Found');
  });

  // 轮播小工具
  router.get('/slider/:sliderHash', wrapAsync(sliderAction));

  // 数据门户页面
  router.get('/dataPortal/:suffix', wrapAsync(dataPortalAction));
  // 编辑页面嵌入的路由应该跟分享页面区分开，否则分享类型的会重定向路由，导致host跟页面匹配不上，message事件无法传递
  router.get('/edit/dataPortal/:suffix', wrapAsync(dataPortalEditAction));
  router.get('/dataPortalShare/:suffix', wrapAsync(dataPortalShareAction));

  // 禁用GBI智能问数时，直接访问 /askgbi 地址时自动跳转到 /groups 页面
  if (yog.conf.sugar.sugarConfig.enableAIAssistant === false) {
    router.get('/askgbi', (req: Request, res: Response) => {
      return res.redirect('/groups');
    });
  }

  // 其他所有url请求都返回 index.tpl
  router.get('*', (req: Request, res: Response) => {
    const dataInTpl: PlainObject = {
      sugarIco: yog.conf.sugar.ico,
      csrfToken: req.csrfToken ? req.csrfToken() : '',
      baiduMapAK: process.env.sugar_baidumap_ak || '7ANZySsmejAIYCyGKi0iKpl0pg08b1wU',
      dsType: databaseType
    };
    // 添加私有部署的版本类型
    if (yog.conf.sugar.deployType === 'private') {
      yog.conf.sugar.sugarConfig.privateType = isPrivateAdvan() ? 'advan' : 'basic';
    }
    dataInTpl['sugarConfig'] = yog.conf.sugar.sugarConfig;

    // 将showGotoNewVersion、showGotoOldVersion、oldVersionActionTag属性挂载到sugarConfig中，控制前端是否展示新旧版切换的按钮
    if (process.env.sugar_show_goto_new_version || process.env.sugar_show_goto_old_version) {
      const other = showOrHideVersionSwitch(req.user);
      dataInTpl['sugarConfig'] = {...yog.conf.sugar.sugarConfig, ...other};
    }

    dataInTpl['loginType'] = yog.conf.sugar.login.type;
    dataInTpl['name'] = yog.conf.sugar.name;
    dataInTpl['company' + 'Name'] = aaa['company' + 'Name'];
    dataInTpl['d' + 'i'] = !!aaa['d' + 'i'];
    dataInTpl['e' + 'Time'] = parseInt((+getDateByString(aaa['la' + 'st' + 'D' + 'ay']) / 6000) as any, 10);
    dataInTpl['s' + 'Time'] = parseInt((+getDateByString(null) / 6000) as any, 10);

    // license中lastDay可能不是合法的日期字符串，会导致bug，因此判断一下，如：2022-01-60 23:59:59
    // 并且加强了license中到期日期的防作弊，用户可能会把new Date给hack掉，因此我们判断了一下日期字符串格式化成日期对象，然后又再次格式化成字符串之后，是否还相等
    if (
      !isValidDate(new Date(aaa['lastDay'])) ||
      formatDate(getDateByString(aaa['lastDay']), '$1-$2-$3 $4:$5:$6') !== aaa['lastDay']
    ) {
      return res.send('license expired：Invalid Date!');
    }

    // 吉利云的Sugar，给的永久版的license，sugar_company是geely，并且不限域名
    // 为了防止license泄漏，加特殊判断，只允许用户在吉利云的sugar那个cce模版中使用，其中有一些特殊的环境变量配置
    if (aaa['company'] === 'geely' && aaa['domain'] === '*' && aaa['lastDay'] && aaa['lastDay'] >= '2024-01-01') {
      if (
        process.env.sugar_is_geely !== 'yes' ||
        !/sugar-redis/i.test(process.env.sugar_redis_host || '') ||
        process.env.sugar_hide_docs_contact !== '1'
      ) {
        return res.send('Geely special license error!');
      }
    }

    // 防止 sugar_query_result_max_rows 设置过大，导致jdbc中int类型maxRowsInt溢出，从而让Impala的jdbc驱动报错：Invalid maximum row size: -**********. Valid range: 0 to Infinity
    if (yog.conf.sugar.queryResultMaxRows > 5000000) {
      return res.send(
        'sugar_query_result_max_size configuration parameter in the env, can not be greater than 5000000'
      );
    }

    // license过期时间大于2049年，表示是永久版，永久版的话前端就不需要判断服务器时间和浏览器时间是否一致
    dataInTpl['sTimePerpetual'] = '';
    if (aaa['lastDay'] && aaa['lastDay'] >= '2049-01-01') {
      dataInTpl['sTimePerpetual'] = '1';
    }
    const deployType = yog.conf.sugar.deployType;
    const hostname = req.hostname;
    const isSaaSOnline = deployType === 'saas'; // && isSaaSDomain(hostname);

    if (isSaaSOnline) {
      dataInTpl['produ' + 'ctConfig'] = yog.conf.sugar.productConfig;
      if (req.company) {
        dataInTpl['produc' + 'tType'] = req.company.service_type || 'free';
        dataInTpl['companyS' + 'tatus'] = req.company.status;
        dataInTpl['companyUs' + 'erNum'] = req.company.user_number;
        dataInTpl['compa' + 'nyD' + 'ashNum'] = req.company.extend_dash_number;
        // 这个时间只有试用版有效
        dataInTpl['com' + 'panyE' + 'Time'] = req.company.expire_time;
      } else {
        dataInTpl['produc' + 'tType'] = 'free';
      }
    }

    // 国际化 分享页面优先使用分享设置的语言
    const lang = req.shareLang && req.shareLang !== 'default' ? req.shareLang : req.session.lang;
    if (lang !== 'zh_cn') {
      dataInTpl['translation'] = language[lang];
    }
    dataInTpl['lang'] = lang;

    // 私有部署下，支持在html页面中插入一段用户自定的js代码
    // 场景：用户接入百度统计、接入用户侧自定义的前端调研小工具等，知乎有使用
    // 定时推送截图、数据预警的后端Puppeteer流程中不需要嵌入这个脚本（会导致百度统计不准，或者出现页面报错之类导致定时推送截图出问题）
    dataInTpl['customJS'] = req.query?.isSugarPuppeteer ? '' : process.env.sugar_custom_javascript || '';

    // 固定浏览器的title，中建科需要
    // 另外前端会在不同的页面中动态改title，目前是在fis.conf中hack了react-document-title库来解决固定问题（这样就不需要改页面react代码，改动最小）
    if (yog.conf.sugar.browserTitle) {
      dataInTpl['browserTitle'] = yog.conf.sugar.browserTitle;
    }
    req.idaasAudit({
      req,
      eventName: 'Enter'
    });
    return res.render('sugar/page/index.tpl', dataInTpl);
  });

  // prometheus记录请求的500错误数，忽略系统api正常错误提示
  router.use((error: any, req: any, res: any, next: any) => {
    const errorType = error.type;
    if (
      !~[
        'NotFoundError',
        'ValidationError',
        'SequelizeValidationError',
        'CSRFError',
        'SessionExpiredError',
        'AuthorizeError',
        'CommonError'
      ].indexOf(errorType) &&
      error.name !== 'SequelizeValidationError' &&
      error.code !== 'EBADCSRFTOKEN'
    ) {
      sugarRequestErrorCounter.inc();
    }
    next(error);
  });
  if (
    yog.conf.sugar.smartAudio.enable ||
    yog.conf.sugar.autoAnalyze.linkType !== 'http' ||
    yog.conf.sugar.undulateAnalysis.linkType !== 'http'
  ) {
    bindSocketIO(router);
  }
};

// ------------------ 增加线上router的复杂度，防止私有部署版的license破解------------------------------------
interface IWithChildren {
  children?: IWithChildren[];
}
/**
 * tree map
 */
export const treeMap = <T extends IWithChildren>(tree: T[], iterator: (item: T, index: number) => T) => {
  return tree.map((item, index) => {
    item = iterator(item, index);

    if (item.children) {
      item.children = treeMap(item.children, iterator);
    }

    return item;
  });
};

export const getReportsTree = async (group_id: number) => {
  const reportTree = await Report.findAll({
    hierarchy: true, // hierarchy的属性，飘红没办法
    where: {
      group_id: group_id
    },
    include: ['creator'],
    order: [['order_index', 'ASC']]
  } as any);
  return reportTree;
};

import Hashids = require('hashids');
const hashids = new Hashids(yog.conf.sugar.hashids || 'sugar', 6, 'abcdefghijklmnopqrstuvwxyz123456789');
let md5sum = crypto.createHash('md5');
md5sum.update(yog.conf.sugar.hashids || 'sugar', 'utf8');
// 根据yog.conf.sugar.hashids算出来的md5值，并且附加在所有生成的hashid的前面，确保yog.conf.sugar.hashids发生改变后，生成的hashid还是唯一的
const hashidsPrefix = md5sum.digest('hex').substring(0, 5);
/**
 * 生成hash，除了和id相关，还有1万亿的随机，基本能充分保证唯一性
 */
export const getHash = (prefix: string, id: number) => {
  if (prefix.substr(-1) !== '_') {
    prefix = prefix + '_';
  }
  let rand = 100000000 * 10000;
  return prefix + hashidsPrefix + '-' + Math.floor(Math.random() * rand).toString(36) + '-' + hashids.encode(id);
};

export const getToken = (username: string, hash: string) => {
  let token = crypto.createHash('md5');
  token.update(username + hash + Math.floor(Math.random() * 10000000).toString(36) + new Date().getTime());
  return token.digest('hex');
};

/**
 * 按照object中的某一个字段，正序或者倒序排序
 */
export function build_sorter(key: string, sortType: string) {
  return function (a: PlainObject, b: PlainObject) {
    let flag = sortType === 'desc' ? b[key] > a[key] : b[key] <= a[key];
    return flag ? 1 : -1;
  };
}

// 非API（即需要返回统一的tpl时，将当前用户信息和组织信息挂载到res.locals上），在login中间件中调用
export const loginLocals = async (req: Request, res: Response, user: IUserInstance, company: ICompanyInstance) => {
  if (!/^\/(api|sugarSocket)\//i.test(req.url)) {
    const sugarCompany = company.email_suffix;
    const userJSON = user.toJSON() as any;

    // 用户是否为首次登录的标记
    userJSON.firstLogin = !!req.session.firstLogin;

    // 无头浏览器的环境下不检查用户空间数
    if (!checkHeadlessByUA(req)) {
      // 当前组织下该用户已加入的空间数
      const myGroups = await user.getGroups({
        where: {
          company_id: company.id
        }
      });
      userJSON.groupCount = myGroups.length;
      if (myGroups.length === 1) {
        const group = myGroups[0] as IGroupInstance & {GroupUser: IGroupUserInstance};
        userJSON.singleGroup = {
          hash: group.hash,
          key: group.group_key,
          name: group.name,
          isAdmin: group.GroupUser?.group_admin || false
        };
      }
    }

    // education账号且是试用
    if ((company.config as any).isEducation && !company.service_type) {
      // 1 => n 所以 where 不需要添加 user_id 的条件
      userJSON.inviteCount = await user.countEducationInviters({
        where: {
          registered: true
        }
      });
    }

    const companyJSON: PlainObject = company.toJSON();
    if (yog.conf.sugar.deployType === 'saas') {
      const expireInfo = await getExpireInfo(company);
      if (expireInfo) {
        companyJSON.expireInfo = expireInfo;
      }
      res.locals.executeLoginTime = req.session.executeLoginTime || '';
    }

    res.locals.myCompanys = userJSON.companys;
    delete userJSON.companys;
    res.locals.user = userJSON;
    res.locals.user.companyAdmin = req.userIsCompanyAdmin;
    res.locals.company = companyJSON;
    res.locals.sugarCompany = sugarCompany;
    // 获取组织的所有管理员信息
    let companyAdmins = await company.getUsers({
      through: {
        where: {
          company_admin: true
        }
      }
    } as any);
    res.locals.companyAdmins = companyAdmins;

    // 向前端返回主html时（不包括分享的大屏和报表页面，它们不会调用login中间件所以不会调用这块逻辑），在session中记录当前用户所处的组织
    // 并且在cookie中记录用户当前所处的组织，以便用户退出登陆了，下次再次登录时会自动进入相应的组织下（这个cookie会在login模块中用到）
    req.session.defaultCompany = sugarCompany as string;
    res.cookie('sugar-company', sugarCompany, {maxAge: 60 * 24 * 3600 * 1000}); // 60天过期
  }
};

// slider action
const sliderAction = async (req: Request, res: Response, next: NextFunction) => {
  if (yog.conf.sugar.sugarConfig.disableBI) {
    return res.render('sugar/page/page-not-found.tpl', {sugarIco: yog.conf.sugar.ico});
  }
  const sliderHash = req.params.sliderHash;
  let slider = await Slider.findOne({
    where: {
      token: sliderHash
    }
  });

  if (!slider) {
    res.status(404).send('没找到对应的轮播页面，请检查您的轮播分享链接是否正确，或者分享已被取消！') as any;
  } else {
    const dataInTpl: PlainObject = {
      sugarIco: yog.conf.sugar.ico
    };
    dataInTpl['sugarConfig'] = yog.conf.sugar.sugarConfig;
    dataInTpl['name'] = yog.conf.sugar.name;
    dataInTpl['urls'] = slider.urls;
    dataInTpl['config'] = slider.config;
    dataInTpl['sliderName'] = slider.name;

    res.render('sugar/page/slider.tpl', dataInTpl);
  }
};

// 类型为分享的数据门户页面
const dataPortalShareAction = async (req: Request, res: Response, next: NextFunction) => {
  const suffix = req.params.suffix;
  let portal = await DataPortal.findOne({
    where: {
      suffix
    },
    include: ['group'] as any
  });

  if (!portal) {
    res.status(404).send('没有找到对应的数据门户，请检查您的访问地址是否正确！') as any;
  } else {
    portal.menus = await getFormatMenus(portal);
    const {sugarConfig, name, ico} = yog.conf.sugar;
    const dataInTpl: PlainObject = {
      sugarIco: portal.icon || ico,
      isEdit: false,
      groupKey: portal?.group?.group_key,
      sugarConfig,
      name: portal.name || name,
      portal
    };

    if (yog.conf.sugar.deployType === 'saas') {
      const company = await portal.group.getCompany();
      if (company) {
        try {
          const expireInfo = await getExpireInfo(company);
          if (expireInfo?.expired) {
            const companyJson: PlainObject = company.toJSON();
            dataInTpl.expiredProductType = productTypeMap[companyJson.serviceType] || '';
          }
        } catch (e) {
          yog.log.fatal('数据门户获取组织过期时间失败，' + e);
        }
      }
    }

    if (portal.type === 'group') {
      // 如果用shareUrl访问group空间
      return res.status(404).send('没有找到对应的数据门户，请检查您的访问地址是否正确！') as any;
    }
    res.render('sugar/page/portal.tpl', dataInTpl);
  }
};

const dataPortalEditAction = async (req: Request, res: Response, next: NextFunction) => {
  // 检查版本是否可用数据门户
  isAvailable(req);
  const suffix = req.params.suffix;
  let portal = await DataPortal.findOne({
    where: {
      suffix
    },
    include: ['group'] as any
  });

  if (!portal) {
    res.status(404).send('没有找到对应的数据门户，请检查您的访问地址是否正确！') as any;
  } else {
    const {sugarConfig, name, ico} = yog.conf.sugar;
    let json = portal.toJSON();

    if (req.query.snapshotHash) {
      const snapshot = await getSnapshot({
        groupHash: portal.group.hash,
        snapshotHash: req.query.snapshotHash
      });
      if (snapshot.config?.main instanceof Object) {
        json = {...json, ...snapshot.config.main};
        portal.menus = json.menus || [];
      }
    }

    const dataInTpl: PlainObject = {
      sugarIco: json.icon || ico,
      isEdit: true,
      groupKey: portal?.group?.group_key,
      sugarConfig,
      name: json.name || name,
      portal: json
    };
    // 编辑页面应该是编辑权限，也不需要判断需要展示的菜单
    const authDataPortal = (await req.authorize.do('canEditDataPortal', portal)) as IAuthCanEditDataPortalReturn;
    dataInTpl.portal.menus = await getFormatMenus(portal, authDataPortal.group.group_key);
    if (yog.conf.sugar.deployType === 'saas' && req.company) {
      try {
        const expireInfo = await getExpireInfo(req.company);
        if (expireInfo?.expired) {
          const companyJson: PlainObject = req.company.toJSON();
          dataInTpl.expiredProductType = productTypeMap[companyJson.serviceType] || '';
        }
      } catch (e) {
        yog.log.fatal('数据门户获取组织过期时间失败，' + e);
      }
    }

    res.render('sugar/page/portal.tpl', dataInTpl);
  }
};

const dataPortalAction = async (req: Request, res: Response, next: NextFunction) => {
  if (yog.conf.sugar.sugarConfig.disableBI) {
    return res.render('sugar/page/page-not-found.tpl', {sugarIco: yog.conf.sugar.ico});
  }
  // 检查版本是否可用数据门户
  isAvailable(req);
  const suffix = req.params.suffix;
  let portal = await DataPortal.findOne({
    where: {
      suffix
    },
    include: ['group'] as any
  });
  if (!portal) {
    res.status(404).send('没有找到对应的数据门户，请检查您的访问地址是否正确！') as any;
  } else if (portal.type === 'share') {
    portal.menus = await getFormatMenus(portal);
    return res.redirect(
      yog.conf.sugar.shareSourceDomain + req.addPathPrefix(req.url.replace(/^\/dataPortal\//, '/dataPortalShare/'))
    );
  } else {
    const {sugarConfig, name, ico} = yog.conf.sugar;
    const dataInTpl: PlainObject = {
      sugarIco: portal.icon || ico,
      isEdit: false,
      groupKey: portal?.group?.group_key,
      sugarConfig,
      name: portal.name || name,
      portal
    };

    if (yog.conf.sugar.deployType === 'saas' && req.company) {
      try {
        const expireInfo = await getExpireInfo(req.company);
        if (expireInfo?.expired) {
          const companyJson: PlainObject = req.company.toJSON();
          dataInTpl.expiredProductType = productTypeMap[companyJson.serviceType] || '';
        }
      } catch (e) {
        yog.log.fatal('数据门户获取组织过期时间失败，' + e);
      }
    }

    // 如果是空间内可见的话，保证用户有空间的读权限
    const authDataPortal = (await req.authorize.do('canReadDataPortal', portal)) as IAuthCanReadGroupDataPortalReturn;
    const {group, isAdmin} = authDataPortal || {};
    if (portal.config.showMenuByLimit) {
      if (isAdmin || group.type >= 1 || req.authorize.isVip) {
        portal.menus = await getFormatMenus(portal, group.group_key);
        return res.render('sugar/page/portal.tpl', dataInTpl);
      }
      portal.menus = await getHasReadLimitMenus(req, portal.menus, group);
    }
    portal.menus = await getFormatMenus(portal, group.group_key);

    // 页面使用记录
    await req.statPageUsage([
      {
        pageName: portal.name || '',
        pageHash: portal.hash || '',
        pageType: 'dataPortal',
        groupId: group.id,
        creatorId: portal.user_id,
        userId: req.user.id,
        useType: 'view'
      }
    ]);

    res.render('sugar/page/portal.tpl', dataInTpl);
  }
};

import {Scheduler} from './cron/scheduler';
import {MailPageTask} from './cron/mail-page-task';
import {findOrCreateFreeSugarExpireScheduler, FreeSugarExpireTask} from './cron/free-sugar-expire-task';
import {findOrCreatePaySugarExpireScheduler, PaySugarExpireTask} from './cron/pay-sugar-expire-task';
// import {
//   findOrCreatePaySugarStickinessDecreaseScheduler,
//   PaySugarStickinessDecreaseTask
// } from './cron/pay-sugar-stickiness-decrease-task';
import {findOrCreateDataCleanScheduler, DataCleanTask} from './cron/data-clean-task';
import {findOrCreateStatCleanScheduler, StatCleanTask} from './cron/stat-clean-task';
import {DataModelStatTask} from './cron/data-model-stat-task';
import {ChartWarningTask} from './cron/chart-warning-task/chart-warning-task';
import {DataEasyFetchTask} from './cron/data-easy-fetch-task';
import {findOrCreateEnterpriseRoleSyncScheduler, EnterpriseRoleSyncTask} from './cron/enterprise-role-sync-task';
import {DataKinshipsUpdateTask} from './cron/data-kinships-update-task';
import {DataKinshipsSyncTask} from './cron/data-kinships-sync-task';
import {
  findOrCreateVpcEndpointCleanAndCheckScheduler,
  VpcEndpointCleanAndCheckTask
} from './cron/vpc-endpoint-clean-and-check-task';
import {MLTrainTask} from './cron/ml-train-task/ml-train-task';
import {MLDumpTask} from './cron/ml-dump-task/ml-dump-task';
import {LoadZipTemplatesToDBTask} from './cron/page-template-zip-task';
import {getSnapshot} from './service/snapshot';
import {bindSocketIO} from './service/socket';
import {isAvailable} from './action/manage/data-portal';
import {ThumbImageTask} from './cron/thumb-image-task';
import {checkHeadlessByUA} from './service/current-request';
import {DataModelESTask} from './cron/data-model-es-task';
import {findOrCreateGBIStatisticsScheduler, GBIStatisticsTask} from './cron/gbi-statistics';

// 作为代理proxy的实例，不启动定时任务
if (yog.conf.sugar.enableTask && !yog.conf.sugar.proxy.runAsProxy) {
  if (!('sugarTask' in global)) {
    (global as any).sugarTask = {};
  }
  // 任务调度，用全局变量主要是方便 yog reload 的时候干掉之前的实例
  for (let taskInstance in (global as any).sugarTask) {
    (global as any).sugarTask[taskInstance].destroy();
  }

  (global as any).sugarTask.scheduler = new Scheduler();
  (global as any).sugarTask.mailPageTask1 = new MailPageTask();

  // saas版本并且高级版支持数据预警或者私有化部署版本并且开启了数据预警时才去实例化数据预警任务
  if (
    (yog.conf.sugar.deployType === 'saas' &&
      !yog.conf.sugar.disableChartWarning &&
      (yog.conf.sugar.productConfig.free?.chartWarning || yog.conf.sugar.productConfig.advan?.chartWarning)) ||
    (yog.conf.sugar.deployType === 'private' && !yog.conf.sugar.disableChartWarning)
  ) {
    (global as any).sugarTask.chartWarningTask = new ChartWarningTask();
  }

  // saas版本并且高级版支持自助取数或者私有化部署版本并且开启了自助取数时才去实例化自助取数任务
  if (
    (yog.conf.sugar.deployType === 'saas' &&
      yog.conf.sugar.enableDataEasyFetch &&
      (yog.conf.sugar.productConfig.free?.dataEasyFetch || yog.conf.sugar.productConfig.advan?.dataEasyFetch)) ||
    (yog.conf.sugar.deployType === 'private' && yog.conf.sugar.enableDataEasyFetch)
  ) {
    (global as any).sugarTask.dataEasyFetchTask = new DataEasyFetchTask();
  }

  // saas 版本才需要执行的邮件任务、资源清理任务，购买到期线索推送任务
  if (yog.conf.sugar.deployType === 'saas') {
    if (yog.conf.sugar.enableFreeExpireMail) {
      // 试用版即将到期邮件提醒任务
      findOrCreateFreeSugarExpireScheduler();
      (global as any).sugarTask.freeSugarExpireTask = new FreeSugarExpireTask();
    }
    // 付费版即将到期销售线索推送任务
    findOrCreatePaySugarExpireScheduler();
    (global as any).sugarTask.PaySugarExpireTask = new PaySugarExpireTask();
    // 付费客户流失预警——黏性下降：推送销售线索
    // findOrCreatePaySugarStickinessDecreaseScheduler();
    // (global as any).sugarTask.PaySugarStickinessDecreaseTask = new PaySugarStickinessDecreaseTask();
  }
  if (yog.conf.sugar.enableDataClean) {
    // 过期资源清理任务
    findOrCreateDataCleanScheduler();
    (global as any).sugarTask.dataCleanTask = new DataCleanTask();
  }
  // 访问统计清理任务
  findOrCreateStatCleanScheduler();
  (global as any).sugarTask.statCleanTask = new StatCleanTask();
  // 数据模型统计/更新unit模型任务
  (global as any).sugarTask.dataModelStatTask = new DataModelStatTask();
  if (yog.conf.sugar.gbi?.enableAIAssistant) {
    (global as any).sugarTask.dataModelESTask = new DataModelESTask();
  }
  // 数据血缘/存储大屏数据血缘任务
  (global as any).sugarTask.dataKinshipsUpdateTask = new DataKinshipsUpdateTask();
  // 同步数据血缘任务
  (global as any).sugarTask.dataKinshipsSyncTask = new DataKinshipsSyncTask();
  // 页面缩略图任务
  (global as any).sugarTask.thumbImageTask = new ThumbImageTask();

  // 企业角色同步任务
  if (yog.conf.sugar.enterpriseRole.enable) {
    findOrCreateEnterpriseRoleSyncScheduler();
    (global as any).sugarTask.enterpriseRoleSyncTask = new EnterpriseRoleSyncTask();
  }

  // vpc-endpoint定时清理，并判断RDS、VPC数据源的ak/sk合法性
  if (yog.conf.sugar.sugarConfig?.b2aDbEnableRegionList?.length) {
    findOrCreateVpcEndpointCleanAndCheckScheduler();
    (global as any).sugarTask.vpcEndpointCleanAndCheckTask = new VpcEndpointCleanAndCheckTask();
  }
  // 决策平台训练任务
  (global as any).sugarTask.MLTrainTask = new MLTrainTask();
  // 离线预测任务
  (global as any).sugarTask.MLDumpTask = new MLDumpTask();
  // 大屏模板zip解析入库任务
  // 任务创建放在middlewares/migration-check-and-mpp-host-update中，因为要确保用户升级了migration，sugar_templates表中包含source_id字段
  (global as any).sugarTask.loadZipTemplatesToDBTask = new LoadZipTemplatesToDBTask();

  // gbi统计任务
  findOrCreateGBIStatisticsScheduler();
  (global as any).sugarTask.GBIStatisticsTask = new GBIStatisticsTask();
}

if (yog.conf.sugar.log?.lifetimeDay) {
  console.log(
    `Create (auto sweep log and tmp files) task: log stores in disk for ${yog.conf.sugar.log?.lifetimeDay} days.`
  );
  // 启动时执行一次，后续每天执行一次，但是作为proxy代理服务时，每十分钟运行一次
  sweepLogAndTmp();
  let ms = 24 * 3600 * 1000;
  if (yog.conf.sugar.proxy.runAsProxy) {
    ms = 600 * 1000;
  }
  setInterval(sweepLogAndTmp, ms);
}

// ------------------  end -----------------
