/**
 * @file 数据模型相关api
 * <AUTHOR>
 */
/* eslint-disable fecs-camelcase */

import {
  Report,
  DataModel,
  Task,
  IDataModelInstance,
  Group,
  sequelize,
  DashboardChart,
  Dashboard,
  sequelizeQuery,
  IDashboardInstance,
  IReportInstance,
  ICustomTableInstance,
  IGroupInstance,
  Database,
  Explore
} from '../models/index';
import {CommonError, AuthorizeError, NotFoundError} from '../lib/errors';
import {
  Request,
  Response,
  NextFunction,
  PlainObject,
  IAuthCanEditDataModelReturn,
  IAuthCanReadGroupReturn,
  IAuthCanUseDataModelReturn,
  IAuthCanReadDashboardReturn,
  IAuthCanReadExploreReturn,
  IAuthCanAIAnalysisReturn
} from '../lib/types';
import getHash from '../lib/hashids';
import {
  DataModelSchema,
  DataModelSchemaMeasure,
  DataModelSchemaDimension,
  DataModelSchemaMenu,
  DataModelSchemaFilterDetail
} from '../data-adapter/data-model-proxy/dialect/base/data-model-schema';
// import {getDbQueryBuilder} from '../data-adapter/data-model-proxy/db-query-builder';
// import {getDbDriver} from '../data-adapter/data-model-proxy/db-driver';
import {
  isAggregatedExpression,
  isTableCalExpression,
  generateIdUpperCase
} from '../data-adapter/data-model-proxy/dialect/utils';
// import {dataTypeFormat} from '../data-adapter/data-model-proxy/cube-process/data-type-format';
// import {dataRename} from '../data-adapter/data-model-proxy/cube-process/data-rename';
import {map, specialFilter} from '../lib/tree';
import {
  getDataModelByHash,
  updateDataModel,
  processDataModelConfigUserEmailOrId,
  getDataModelsByWhere,
  getFieldNumOfDM,
  validateDataModelExploreAuth,
  validateDataModelShareAuth,
  getConfigOfDataModel,
  getDataModelByWhere,
  updateDataModelMinConfig
} from '../service/data-model';
import {getMeRolesToCurrentGroup} from '../service/current-request';
import {
  getDataByModel,
  DataModelParams,
  dataModelProxy,
  DataModelProxyReturn,
  combineDataModelAndReportOrDashboardDmschema
} from '../data-adapter/data-model-proxy/data-model-proxy';
import {getDatabaseDriver, getTableSchemaRet} from './database';
import {escapeSql} from '../data-adapter/sql-proxy/sql-security';
import {getCustomTableByWhere, getCustomTablesByHash} from '../service/custom-table';
import {validateShareAuth} from './condition-data';
import {getDimDistinctData} from '../cron/data-model-stat-task';
import {saveDataModelKinships} from '../service/data-kinships';
import {dataEasyFetchUseVerification} from '../service/data-easy-fetch';
import {getUsingFieldsFromDataConfig} from '../service/analyze/analyze';
import {getReportDatamodelConfig} from '../service/report';
import {getDashboardDatamodelConfig} from '../service/dashboard';
import {sugar_t} from '../service/language/translate';
import {sqlFuncDemos} from '../lib/sql-func-demo';
import uniq = require('lodash/uniq');
import pick = require('lodash/pick');
import {addSnapshot, getSnapshot} from '../service/snapshot';
import {formatDate, hasDILicense, isPrivateAdvan, sugar_count} from '../lib/common-helper';
import {canUseDatabaseInTrain} from '../service/ml/ml-train';
import {canUseDatabaseInDump} from '../service/ml/ml-dump';
import {canUseDMLearns, checkAIAssistant, updateDMLearnTask} from '../service/data-model-learn';
import {processDataModelSchema} from './openapi/internal/data-model-service';

export type IDMMapBYAlias = {
  dimension: {
    [prop: string]: {node: DataModelSchemaDimension; id: string} | undefined;
  };
  measure: {
    [prop: string]: {node: DataModelSchemaMeasure; id: string} | undefined;
  };
};

const DATA_STATISTICS_DELAY = 0; // 这个任务在智能问数中也在使用，改为2min缩短一下用户等待时间
const NLP_SKILL_FIELD_NUMBER_LIMIT = 400;
export const NLP_NAME = 'AI问答';

/**
 * 获取某空间下所有的数据模型树形列表
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const treeList = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash = req.params.groupHash || '';
  const noIcon = req.query.noIcon || '';
  const onlyLeaf = req.query.onlyLeaf || '';
  let groupId = -1;
  let dataModelHashs: string[] | null = null;
  let authDataModelIds: number[] | null = null;
  let modelTree: any;
  let isAdmin = false;
  let isSuper = false;
  //探索页的数据模型拉取
  if (req.query.exploreReportHash) {
    let hash = req.query.exploreReportHash;
    if (hash.startsWith('group_explore_')) {
      const objGroup = await req.authorize.canReadGroup(groupHash);
      if (!objGroup) {
        throw new NotFoundError('无权限查看该报表空间！');
      }
      if (!objGroup.group || !objGroup.group.config.groupExplore) {
        throw new NotFoundError('该空间未开启空间自助探索页');
      }
      if (!objGroup.group.config.dataModelHashs || !objGroup.group.config.dataModelHashs.length) {
        throw new NotFoundError('空间自助探索页未配置数据模型，请联系空间管理员');
      }
      groupId = objGroup.group.id;
      dataModelHashs = objGroup.group.config.dataModelHashs;
    } else {
      const obj = await req.authorize.canReadReport(hash);
      if (!obj) {
        throw new AuthorizeError('您无权限查看该数据模型');
      }
      if (obj.report.type !== 3) {
        throw new AuthorizeError('您查看的不是探索页面');
      }
      groupId = obj.group.id;
      dataModelHashs = obj.report.config.dataModelHashs || [];
    }
  } else if (req.query.dashboardHash) {
    let hash = req.query.dashboardHash;
    // 浏览模式，根据大屏中「智能音频」图表配置的数据模型确定提问范围
    const obj = (await req.authorize.do('canReadDashboard', hash)) as IAuthCanReadDashboardReturn;
    groupId = obj.group.id;
    let smartAudioChart = await DashboardChart.findOne({
      where: {
        dashboard_id: obj.dashboard.id,
        type: 'smart-audio'
      }
    });
    if (smartAudioChart) {
      dataModelHashs = (smartAudioChart.config as any).dataModelHashs || null;
    }
  } else if (req.query.ernieHash) {
    //智能问数页的数据模型拉取
    let hash = req.query.ernieHash;
    if (hash.startsWith('ex_')) {
      const objGroup = (await req.authorize.do('canReadExplore', hash)) as IAuthCanReadExploreReturn;
      if (!objGroup) {
        throw new NotFoundError('您无权限查看该数据模型！');
      }
      groupId = objGroup.group.id;
      dataModelHashs = objGroup.explore.config.dataModelHashes;
    }
  } else {
    const obj = (await req.authorize.do('canReadGroup', groupHash)) as IAuthCanReadGroupReturn;
    isAdmin = obj.isAdmin;
    groupId = obj.group.id;
    if (!isAdmin) {
      //当前空间内对应数据模型
      const dataModelIds: Array<{id: number}> = await sequelizeQuery(
        {
          mysql: 'SELECT id FROM `sugar_data_models`' + ' WHERE `sugar_data_models`.group_id = :groupId',
          postgres: 'SELECT id FROM sugar_data_models' + ' WHERE sugar_data_models.group_id = :groupId'
        },
        {
          replacements: {
            groupId: groupId
          },
          type: sequelize.QueryTypes.SELECT
        }
      );
      const meDataModelIds = dataModelIds.map(dataModel => dataModel.id);
      if (dataModelIds) {
        // 角色的权限
        // 查找用户所属的角色
        const roles = await getMeRolesToCurrentGroup(req.user, req, groupId);
        const roleIds = roles.map(role => role.id);
        const reportIds = meDataModelIds.length ? meDataModelIds : [0];
        //图表编辑时只拉取有使用权限的数据模型列表
        if (roleIds.length) {
          // 角色超级权限判断：超级数据开发或超级分析师可以使用所有数据模型
          const superInfo = await req.authorize.getRoleSuperAuth(roleIds, ['superDataDevelop', 'superPage']);
          if (superInfo) {
            authDataModelIds = null;
            isSuper = true;
          } else {
            //查找角色有权限使用的数据模型
            const myRolePermissions = await req.authorize.getRoleAuth(roleIds, ['modelUse'], reportIds);
            if (myRolePermissions.length) {
              authDataModelIds = myRolePermissions.map(model => model.report_id);
            }
          }
        }
        //如果角色可以判断出超级权限，就不需要再查用户信息了，直接返回完整的树，否则再查用户信息
        if (!isSuper) {
          //用户的权限
          const superInfo = await req.authorize.getUserSuperAuth(groupId, ['superDataDevelop', 'superPage']);
          if (superInfo) {
            authDataModelIds = null;
          } else {
            //查找用户有使用权限的数据模型
            const myModelPermissions = await req.authorize.getUserAuth(groupId, ['modelUse'], reportIds);
            authDataModelIds = (authDataModelIds || []).concat(
              (myModelPermissions || []).map(model => model.report_id)
            );
          }
        }
      }
    }
  }

  modelTree = await DataModel.findAll({
    hierarchy: true, // hierarchy的属性，飘红没办法
    where: {
      group_id: groupId
    },
    order: [['order_index', 'ASC']],
    attributes: {exclude: ['config']} // config字段不select
  } as any);

  if (dataModelHashs !== null) {
    modelTree = specialFilter(modelTree as any, (node: any) => {
      return !!(dataModelHashs && dataModelHashs.includes(node.hash));
    });
  }
  if (authDataModelIds !== null) {
    modelTree = specialFilter(modelTree as any, (node: any) => {
      return !!(authDataModelIds && authDataModelIds.includes(node.id));
    });
  }
  // 下面的几个字段必须要，前端有依赖
  const ret = map(modelTree, (node: any) => {
    const item: PlainObject = {
      label: node.name,
      name: node.name,
      value: node.hash,
      isDir: !node.type, // 是否文件夹
      isLeaf: !!node.type, // 是否非文件夹
      icon: noIcon ? '' : !node.type ? 'fa fa-folder' : 'fa fa-cubes', // 前端树形展示时的icon,
      nlpOpen: node.nlp_open,
      nlpState: node.nlp_state,
      sugQuestion: node.sug_question,
      sugQuestionLLM: node.sug_question_llm,
      children: node.children
    };
    if (onlyLeaf) {
      item.disabled = item.isDir;
    }
    return item;
  });
  res.sugarJson(ret);
};

/**
 * 获取某分享智能问数下的数据模型列表
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const treeListShareErnie = async (req: Request, res: Response, next: NextFunction) => {
  const ernieHash = req.params.ernieHash;
  const ernieShareToken = req.params.ernieShareToken;
  let groupId = -1;
  let dataModelHashs: string[] | null = null;

  // 验证权限
  let haveAuthorize = false;
  if (ernieHash && ernieShareToken) {
    const ernie = await Explore.findOne({
      where: {
        hash: ernieHash
      },
      include: ['group'] as any
    });
    if (!ernie) {
      throw new NotFoundError('请检查您的智能问数分享URL');
    }

    haveAuthorize = validateShareAuth(req, ernie, ernieShareToken);
    if (!haveAuthorize) {
      throw new NotFoundError('请检查您的页面分享URL');
    }
    dataModelHashs = ernie.config.dataModelHashes || [];
    groupId = ernie.group_id;
  }

  let modelTree = await DataModel.findAll({
    hierarchy: true, // hierarchy的属性，飘红没办法
    where: {
      group_id: groupId
    },
    order: [['order_index', 'ASC']],
    attributes: {exclude: ['config']} // config字段不select
  } as any);

  if (dataModelHashs !== null) {
    modelTree = specialFilter(modelTree as any, (node: any) => {
      return !!(dataModelHashs && dataModelHashs.includes(node.hash));
    });
  }
  // 下面的几个字段必须要，前端有依赖
  const ret = map(modelTree, (node: any) => {
    const item: PlainObject = {
      label: node.name,
      name: node.name,
      value: node.hash,
      isDir: !node.type, // 是否文件夹
      isLeaf: !!node.type, // 是否非文件夹
      icon: !node.type ? 'fa fa-folder' : 'fa fa-cubes', // 前端树形展示时的icon,
      nlpOpen: node.nlp_open,
      nlpState: node.nlp_state,
      sugQuestion: node.sug_question,
      children: node.children
    };
    item.disabled = item.isDir;

    return item;
  });

  res.sugarJson(ret);
};

/**
 * 获取某分享探索页下的数据模型列表
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const treeListShare = async (req: Request, res: Response, next: NextFunction) => {
  const reportHash = req.params.reportHash;
  const reportShareToken = req.params.reportShareToken;
  const dashboardHash: string = req.params.dashboardHash;
  const dashboardShareToken = req.params.dashboardShareToken;
  let groupId = -1;
  let dataModelHashs: string[] | null = null;

  // 验证权限
  let haveAuthorize = false;
  if (reportHash && reportShareToken) {
    const report = await Report.findOne({
      where: {
        hash: reportHash
      },
      include: ['group'] as any
    });
    if (!report) {
      throw new NotFoundError('请检查您的探索页分享URL');
    }
    if (report.type !== 3) {
      throw new AuthorizeError('您查看的不是探索页面');
    }

    haveAuthorize = validateShareAuth(req, report, reportShareToken);
    if (!haveAuthorize) {
      throw new NotFoundError('请检查您的页面分享URL');
    }
    dataModelHashs = report.config.dataModelHashs || [];
    groupId = report.group_id;
  } else if (dashboardHash && dashboardShareToken) {
    const dashboard = await Dashboard.findOne({
      where: {
        hash: dashboardHash,
        token: dashboardShareToken,
        auth_type: {
          $in: [1, 2, 3]
        }
      },
      include: ['group'] as any
    });
    if (!dashboard) {
      throw new NotFoundError('请检查您的大屏分享URL');
    }
    haveAuthorize = validateShareAuth(req, dashboard, dashboardShareToken);
    if (!haveAuthorize) {
      throw new NotFoundError('请检查您的页面分享URL');
    }
    // 浏览模式，根据大屏中「智能音频」图表配置的数据模型确定提问范围
    let smartAudioChart = await DashboardChart.findOne({
      where: {
        dashboard_id: dashboard.id,
        type: 'smart-audio'
      }
    });
    if (smartAudioChart) {
      dataModelHashs = (smartAudioChart.config as any).dataModelHashs || null;
    }
    groupId = dashboard.group_id;
  }

  let modelTree = await DataModel.findAll({
    hierarchy: true, // hierarchy的属性，飘红没办法
    where: {
      group_id: groupId
    },
    order: [['order_index', 'ASC']],
    attributes: {exclude: ['config']} // config字段不select
  } as any);

  if (dataModelHashs !== null) {
    modelTree = specialFilter(modelTree as any, (node: any) => {
      return !!(dataModelHashs && dataModelHashs.includes(node.hash));
    });
  }
  // 下面的几个字段必须要，前端有依赖
  const ret = map(modelTree, (node: any) => ({
    name: node.name,
    value: node.hash,
    isDir: !node.type, // 是否文件夹
    isLeaf: !!node.type, // 是否非文件夹
    icon: !node.type ? 'fa fa-folder' : 'fa fa-cubes', // 前端树形展示时的icon,
    nlpOpen: node.nlp_open,
    nlpState: node.nlp_state,
    sugQuestion: node.sug_question,
    sugQuestionLLM: node.sug_question_llm,
    children: node.children
  }));
  res.sugarJson(ret);
};

/**
 * 获取某数据模型的具体信息
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const show = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.params.dataModelHash || '';
  const exploreReportHash = req.query.exploreReportHash;
  const dashboardHash = req.query.dashboardHash;
  const reportOrDashboardHash = req.query.reportOrDashboardHash || '';
  const dataEasyFetch = req.query.dataEasyFetch || '';
  const train = req.query.train || '';
  const dump = req.query.dump || '';
  const ernieHash = req.query.ernieHash || '';
  const consumePageHash = req.query.consumePageHash || '';

  const isEdit = req.query.edit;
  const snapshotHash = req.query.snapshotHash;
  let group = null;
  const dataModel = await getDataModelByWhere({hash: dataModelHash}, undefined, true, true);

  if (!dataModel) {
    throw new NotFoundError('没有找到该数据模型，可能已被删除！');
  }
  if (exploreReportHash) {
    // 请求来自探索页
    const obj = await validateDataModelExploreAuth(dataModelHash, exploreReportHash, req, 'explore');
    if (!obj) {
      throw new AuthorizeError('您无权限查看该数据模型');
    }
    group = obj.group;
  } else if (dashboardHash) {
    // 请求来自大屏非分享浏览页
    const obj = await validateDataModelExploreAuth(dataModelHash, dashboardHash, req, 'dashboard');
    if (!obj) {
      throw new AuthorizeError('您无权限查看该数据模型');
    }
    group = obj.group;
  } else if (ernieHash) {
    const obj = await validateDataModelExploreAuth(dataModelHash, ernieHash, req, 'AIExplore');
    if (!obj) {
      throw new AuthorizeError('您无权限查看该数据模型');
    }
    group = obj.group;
  } else if (consumePageHash) {
    // 报表消费场景：有报表的分析权限就可以查看数据模型
    const obj = (await req.authorize.canAIAnalysis(consumePageHash)) as IAuthCanAIAnalysisReturn;
    if (!obj) {
      throw new AuthorizeError('您无权限查看该数据模型');
    }
    group = obj.group;
  } else {
    const obj = isEdit
      ? ((await req.authorize.do('canEditDataModel', dataModel)) as IAuthCanEditDataModelReturn)
      : ((await req.authorize.do('canUseDataModel', dataModel)) as IAuthCanUseDataModelReturn);
    group = obj.group;
  }

  if (dataModel.group_id !== group.id) {
    throw new CommonError('获取数据模型信息失败：API的url错误，空间和数据模型不对应');
  }

  // 如果是自助取数中获取数据模型的详细信息时需要先验证自助取数是否可以使用
  if (dataEasyFetch) {
    const verificationObj = await dataEasyFetchUseVerification(dataModel);
    if (!verificationObj.canUse) {
      throw new CommonError(verificationObj.msg);
    }
  }

  // 如果是训练页面获取数据模型的详细信息时需要先验证是否可以使用
  if (train) {
    const verificationObj = await canUseDatabaseInTrain(dataModel);
    if (!verificationObj.canUse) {
      throw new CommonError(verificationObj.msg);
    }
  }

  if (dump) {
    const verificationObj = await canUseDatabaseInDump(dataModel);
    if (!verificationObj.canUse) {
      throw new CommonError(verificationObj.msg);
    }
  }

  // 对行级别权限以及数据脱敏的用户邮箱进行处理，传到前段时需要用户id转邮箱
  const dataModelSchema: DataModelSchema = dataModel.config as any;
  dataModel.config = (await processDataModelConfigUserEmailOrId(dataModelSchema, group, 'get')) as any;
  const pageData = await reportOrDashboard(reportOrDashboardHash, dataModelHash);
  if (pageData) {
    dataModel.config = combineDataModelAndReportOrDashboardDmschema(dataModel.config as any, pageData) as any;
  }
  let types: number[] = [];
  const databases = await Database.findAll({
    where: {
      group_id: group.id,
      hash: {
        $in: dataModel.database_hashes.split(',')
      }
    }
  });

  if (databases.length) {
    databases.forEach(database => {
      types.push(database.type);
    });
    types = uniq(types);
  }

  // 判断当前用户是否能够编辑该数据模型，如果能够编辑，在数据中加上currentUserCanEdit字段
  const currentUserCanEdit = await req.authorize.canEditDataModel(dataModelHash);
  let ret = dataModel.toJSON();

  // 获取历史版本配置并覆盖到当前页面
  if (snapshotHash) {
    if (req.productConfig && !req.productConfig.snapshot) {
      throw new CommonError('当前版本无法操作历史版本，请升级到高级版！');
    }
    const snapshot = await getSnapshot({
      groupHash: group.hash,
      snapshotHash: snapshotHash
    });
    if (snapshot.config?.main instanceof Object) {
      ret = {...ret, ...snapshot.config.main};
    }
  }

  const retExtend = {
    ...ret,
    dbType: types[0] ?? -1
  };

  if (!!isEdit) {
    await req.statPage(dataModelHash, group.id, 0, 16);

    // 页面使用记录
    if (dataModel.type) {
      await req.statPageUsage([
        {
          pageName: dataModel.name || '',
          pageHash: dataModel.hash || '',
          pageType: 'dashboard',
          groupId: group.id,
          creatorId: dataModel.user_id,
          userId: req.user.id,
          useType: 'view'
        }
      ]);
    }
  }

  if (currentUserCanEdit) {
    return res.sugarJson({
      ...retExtend,
      currentUserCanEdit: true
    });
  } else {
    return res.sugarJson(retExtend);
  }
};

/**
 * 获取某数据模型的具体信息
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const shareShow = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.params.dataModelHash || '';
  const reportHash = req.params.reportHash;
  const dashboardHash: string = req.params.dashboardHash;
  const ernieHash: string = req.params.ernieHash;

  if (reportHash) {
    const reportShareToken = req.params.reportShareToken;
    let obj = await validateDataModelShareAuth(dataModelHash, reportHash, reportShareToken, req);
    if (obj) {
      return res.sugarJson(obj.dataModel);
    }
  } else if (dashboardHash) {
    let token = req.params.dashboardShareToken;
    let obj = await validateDataModelShareAuth(dataModelHash, dashboardHash, token, req, 'dashboard');
    if (obj) {
      return res.sugarJson(obj.dataModel);
    }
  } else if (ernieHash) {
    let token = req.params.ernieShareToken;
    let obj = await validateDataModelShareAuth(dataModelHash, ernieHash, token, req, 'ernie');
    if (obj) {
      return res.sugarJson(obj.dataModel);
    }
  }
  throw new AuthorizeError('您无权限查看该数据模型');
};

/**
 * 更新数据模型
 */
export const put = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.params.dataModelHash || '';
  const name = req.body.name || '';
  const config = req.body.config || '';

  // 判断是否有更新数据模型权限
  const obj = (await req.authorize.do('canEditDataModel', dataModelHash, true)) as IAuthCanEditDataModelReturn;
  const dataModel = obj.dataModel;

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('保存数据模型信息失败：API的url错误，空间和数据模型不对应');
  }

  // 判断名称是否重复
  const currentDataModel = await DataModel.findOne({
    where: {
      name,
      group_id: obj.group.id,
      hash: {
        $not: dataModelHash
      }
    }
  });
  if (currentDataModel) {
    throw new CommonError('模型名称已存在，请修改后再保存');
  }

  // saas 版本 predictModel 验证
  if (yog.conf.sugar.deployType === 'saas' && config?.predicts?.length && !req.productConfig?.predictModel) {
    throw new CommonError(`您所在的「${req.productConfig?.name}」组织不支持使用预测服务功能，请升级版本`);
  }
  // 两账号如果没有开启 DI 则不支持使用预测服务，但可以购买 DI, 然后开启
  if (yog.conf.sugar.deployType === 'private' && config?.predicts?.length && !isPrivateAdvan() && !hasDILicense()) {
    throw new CommonError(`您的「${yog.conf.sugar.name}」实例不支持使用预测服务功能，请联系管理员`);
  }
  // 前端拦截了没有 openDI，后端也拦截一下
  config?.predicts?.forEach((predict: any) => {
    if (predict.modelType === 'train' && !hasDILicense()) {
      throw new CommonError(`您的「${yog.conf.sugar.name}」实例不支持使用训练预测服务相关功能，请联系管理员`);
    }
    if (predict.modelType === 'bml' && !hasDILicense() && !req.productConfig?.predictModel) {
      if (yog.conf.sugar.deployType === 'private') {
        throw new CommonError(`您的「${yog.conf.sugar.name}」实例不支持使用BML/EasyDL预测服务相关功能，请联系管理员`);
      } else {
        throw new CommonError(`您所在的「${req.productConfig?.name}」组织不支持使用BML/EasyDL预测服务功能，请升级版本`);
      }
    }
  });
  if (config?.predicts?.length > 3) {
    throw new CommonError('单个数据模型最多添加 3 个预测服务');
  }

  // 上面的dataModel必须那样findOne，不能用service中的getDataModelByHash来获取，因为下面的updateDataModel方法中要用到，不能替换dataModel中的config为真正的JSON格式的config
  // 因此下面一行 getConfigOfDataModel 方法的第二个参数也必须为 true，不能更改dataModel中的config字段
  const dmOldConfig = await getConfigOfDataModel(dataModel, true);
  // 写日志，记录修改数据模型时之前老的config的完整信息
  req.log('data_model_update', 'notice', {
    dataModelHash,
    oldConfig: JSON.stringify(dmOldConfig)
  });

  if (
    yog.conf.sugar.deployType === 'saas' &&
    !req.productConfig.dataModelFuzzy &&
    config &&
    config.fuzzy &&
    config.fuzzy.open &&
    config.fuzzy.list &&
    config.fuzzy.list.length
  ) {
    throw new CommonError(
      `您的数据模型设置了数据脱敏，「${req.productConfig.name}」组织不支持数据脱敏，请升级组织版本或删除已设置的数据脱敏`
    );
  }

  await generateDMTrainTask(req, dataModel, config);

  // if (yog.conf.sugar.deployType !== 'private' || yog.conf.sugar.storeResourceInBos) {
  await DataModel.update(
    {
      updater_id: req.user.id,
      updated_at: new Date()
    },
    {
      where: {id: dataModel.id}
    }
  );
  // }

  // 更新 min_config，由于下面 updateDataModel 方法还需要保存，这里就不保存了
  await updateDataModelMinConfig(dataModel, config, false);

  let newDataModel = await updateDataModel(dataModel, name, config, undefined, obj.group);

  // dataModel-es的任务执行挪到dataModel-es任务执行之后，这里注释掉
  // if (checkAIAssistant(req, true)) {
  //   // 数据模型发生改动，有问数配置的学习任务需要重新开始学习
  //   await updateDMLearnTask({group: obj.group, dataModel: dataModel});
  // }

  //保存数据模型血缘关系
  await saveDataModelKinships(dataModel, config, obj.group.id);

  if (yog.conf.sugar.sugarConfig.enableSnapshot && (!req.productConfig || req.productConfig.snapshot)) {
    await addSnapshot(req, res, next, {
      groupId: obj.group.id,
      groupHash: obj.group.hash,
      userId: req.user.id,
      resourceHash: dataModelHash,
      resourceType: 'dataModel',
      config: JSON.stringify({
        main: pick(newDataModel, ['name', 'remark', 'config'])
      })
    });
  }

  await req.statResourceAudit({
    hash: dataModelHash,
    resourceEntity: dataModel,
    type: 'UPDATE_DATAMODEL',
    group_id: obj.group.id
  });

  // 页面使用记录
  if (dataModel.type) {
    await req.statPageUsage([
      {
        pageName: dataModel.name || '',
        pageHash: dataModel.hash || '',
        pageType: 'dataModel',
        groupId: obj.group.id,
        creatorId: dataModel.user_id,
        userId: req.user.id,
        useType: 'edit'
      }
    ]);
  }

  req.idaasAudit({
    req,
    eventName: 'EditDataModel',
    resources: [
      {
        resourceType: 'resource-groupHash',
        resourceName: obj.group.hash
      },
      {
        resourceType: 'resource-groupName',
        resourceName: obj.group.name
      },
      {
        resourceType: 'resource-databaseHashes',
        resourceName: dataModel.database_hashes
      },
      {
        resourceType: 'resource-dataModelHash',
        resourceName: dataModel.hash
      },
      {
        resourceType: 'resource-dataModelName',
        resourceName: dataModel.name
      }
    ]
  });

  return res.sugarJson({...newDataModel.toJSON(), currentUserCanEdit: true});
};

// 创建一个数据统计 / nlp 推送任务
export const generateDMTrainTask = async (
  req: Request,
  dataModel: IDataModelInstance,
  newDataModelSchema: DataModelSchema
) => {
  console.log('xxxxxxxxxxxxxxxxxxxx');
  let taskConfig = {statistics: true, smartAnswer: true, companyId: req.company.id};

  if (dataModel.nlp_open !== 1) {
    taskConfig.smartAnswer = false;
  }

  if (yog.conf.sugar.deployType === 'saas') {
    if (!req.productConfig.smartAnswer) {
      taskConfig.smartAnswer = false;
    }
    if (!~req.productConfig.charts.indexOf('smart-chart')) {
      taskConfig.statistics = false;
    }
  }
  if (!yog.conf.sugar.enableNLP) {
    taskConfig.smartAnswer = false;
  }

  // 如果真的还需要智能问答，那么检查下本组织/空间的数据列限制
  if (taskConfig.smartAnswer) {
    let groupIds = [dataModel.group_id];
    // 如果是 saas 版则要统计组织下所有开启了nlp功能的数据模型
    if (yog.conf.sugar.deployType === 'saas') {
      groupIds = (await Group.findAll({where: {company_id: req.company.id}, attributes: ['id']})).map(g => g.id);
    }
    let dataModels = await getDataModelsByWhere({
      group_id: {
        $in: groupIds
      },
      type: 1,
      nlp_open: 1
    });
    let fieldNum = getFieldNumOfDM(newDataModelSchema);
    for (let dm of dataModels) {
      fieldNum += getFieldNumOfDM(dm.config as any);
    }
    if (fieldNum > NLP_SKILL_FIELD_NUMBER_LIMIT) {
      throw new CommonError(
        `同时开启【「${NLP_NAME}」】功能的数据模型中总字段数目不能超过「${NLP_SKILL_FIELD_NUMBER_LIMIT}」个，当前有「${fieldNum}」个`
      );
    }
  }
  console.log('bbbbbb', taskConfig.statistics, taskConfig.smartAnswer);
  if (taskConfig.statistics || taskConfig.smartAnswer) {
    console.log('cccccccc');
    let task = await Task.findOne({
      where: {
        type: 'dataModel-update',
        group_id: dataModel.group_id,
        schedule_id: dataModel.id
      }
    });

    console.log('eeeeee', task);

    if (!task) {
      task = await Task.create({
        type: 'dataModel-update',
        priority: 4,
        config: JSON.stringify(taskConfig),
        group_id: dataModel.group_id,
        schedule_id: dataModel.id,
        run_at: new Date(+new Date() + DATA_STATISTICS_DELAY),
        status: 0
      });
      task.hash = getHash('task_', task.id);
    } else {
      task.config = JSON.stringify(taskConfig);
      task.run_at = new Date(+new Date() + DATA_STATISTICS_DELAY);
      task.status = 0;
    }

    console.log('ffffffxxxxxx', task);


    dataModel.nlp_state = 0;

    await task.save();
  }
};

const hasSameNameDM = (data: IDMMapBYAlias, name: string) => {
  const ret = data.dimension[name] || data.measure[name];
  if (ret) {
    return ret;
  }
  return false;
};

/**
 * 获取schema中所有的维度和度量map
 * @param schema
 */
const getDMMapByAlias = (schema: DataModelSchema) => {
  const ret: IDMMapBYAlias = {
    dimension: {},
    measure: {}
  };

  Object.values(schema.dimensions).forEach((dim: PlainObject, index: number) => {
    if (dim.type === 'dimension') {
      const name = dim.alias || dim.field;
      if (hasSameNameDM(ret, name)) {
        throw new CommonError(`模型中有同名维度或度量「${name}」`);
      } else {
        (ret.dimension[name] as any) = {
          node: dim,
          id: Object.keys(schema.dimensions)[index]
        };
      }
    }
  });

  Object.values(schema.measures).forEach((mea: PlainObject, index: number) => {
    const name = mea.alias || mea.field;
    if (hasSameNameDM(ret, name)) {
      throw new CommonError(`模型中有同名维度或度量「${name}」`);
    } else {
      (ret.measure[name] as any) = {
        node: mea,
        id: Object.keys(schema.dimensions)[index]
      };
    }
  });
  return ret;
};

/**
 * 服务端获取单个table的schema
 * @param req
 */
const getTableSchemaInfo = async (req: Request, obj: PlainObject) => {
  const databaseHash = req.params.databaseHash;
  let table = req.query.table;
  let tableId = req.query.tableId;
  let customTableHash = req.query.customTableHash || '';
  if (!table) {
    throw new CommonError('未传入数据表！');
  }
  try {
    const driver = await getDatabaseDriver(req, false, obj, req.query);
    table = escapeSql(table).replace(/;/g, ''); // 对table名称做安全性替换，防止SQL注入
    if (!driver.getTableSchema) {
      throw new CommonError(`系统还不支持对「${driver.dbType}」进行数据建模！`);
    }

    let result = [];
    // customTableHash有值时说明是自定义SQL，直接去自定义SQL视图查找
    if (customTableHash) {
      const customTable = await getCustomTableByWhere({
        hash: customTableHash
      });

      if (customTable && customTable.content) {
        const content = customTable.content as any;
        table = customTable.name;
        content ? (result = content?.fields) : null;
      }
    } else {
      result = await driver.getTableSchema(table);
    }

    if (!result || !result.length) {
      throw new CommonError(`该数据表「${table}」中无任何字段`);
    }
    // table的schema中，对每个字段加一个id，这个id是通过数据源hash、表名、字段名算的md5（全部大写，并且加了SG前缀），保证唯一性并且每次都一样
    return getTableSchemaRet(databaseHash, table, result, req.query.homoId || '', req.body.config, tableId);
  } catch (e) {
    throw new CommonError(`${table}: ${e.message}`);
  }
};

/**
 * 同步表结构，兼容了同源异库以及跨源的数据模型
 * @param req
 * @param res
 * @param next
 */
export const syncStructure = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.params.dataModelHash || '';
  const name = req.body.name || '';
  let config: DataModelSchema = req.body.config || '';

  // 判断有没有数据模型编辑权限
  const obj = (await req.authorize.do('canEditDataModel', dataModelHash, true)) as IAuthCanEditDataModelReturn;
  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('查看数据模型数据失败：空间和数据模型不对应');
  }
  let dimOrMeaIds: string[] = [], // 度量和维度的id集合
    removeDimOrMeaIds: string[] = []; // 删除的度量或者维度的id

  // 写日志，记录同步表结构之前的完整schema信息
  req.log('data_model_sync_structure', 'notice', {
    dataModelHash,
    oldConfig: JSON.stringify(config)
  });

  for (let index in config.tables) {
    const tableCurrent = config.tables[index];
    req.query = {};
    req.query.table = tableCurrent.tableName;
    req.query.tableId = tableCurrent.tableId;
    req.query.customTableHash = tableCurrent.customTableHash || '';
    req.params.databaseHash = tableCurrent.dbHash;
    // 兼容同源异库
    if (tableCurrent.homoId) {
      req.query.homoId = tableCurrent.homoId;
      const homo = (config.homologous || []).find(item => item.homoId === tableCurrent.homoId);
      if (homo) {
        for (const key in homo.config) {
          if (Object.prototype.hasOwnProperty.call(homo.config, key)) {
            req.query[key] = (homo.config as any)[key];
          }
        }
      }
    }
    const tableSchema: any = await getTableSchemaInfo(req, obj);
    const dmMapByAlias = getDMMapByAlias(config);

    for (let j in tableSchema) {
      const col = tableSchema[j];
      dimOrMeaIds.push(col.id);

      let alias = col.name;
      if (
        (!config.dimensions.hasOwnProperty(`${col.id}`) || !config.measures.hasOwnProperty(`${col.id}`)) &&
        hasSameNameDM(dmMapByAlias, alias)
      ) {
        let customTable;
        if (tableCurrent.customTableHash) {
          customTable = await getCustomTableByWhere({
            hash: tableCurrent.customTableHash
          });
        }
        alias = `${alias}(${customTable ? customTable.name : tableCurrent.tableName})`;
        if (hasSameNameDM(dmMapByAlias, alias)) {
          alias = `${alias}_1`;
        }
      }

      if (~['string', 'date', 'datetime', 'timestamp'].indexOf(col.type)) {
        // 维度，表中新增了字段
        if (!config.dimensions.hasOwnProperty(`${col.id}`) && !config.measures.hasOwnProperty(`${col.id}`)) {
          // 防止维度度量转换

          // 防止config.dimensionMenu为空数组，空数组时进行赋初值
          !config.dimensionMenu.length
            ? config.dimensionMenu.push({
                type: 'menu',
                menuId: generateIdUpperCase(true),
                name: tableCurrent.tableName,
                nodes: []
              })
            : null;
          // 将col.id放在对应的dimensionMenu中
          const menuIndex = config.dimensionMenu.findIndex(val => {
            if (val.nodes?.length) {
              const dimId = val.nodes[0];
              return config.dimensions[dimId]?.tableId === tableCurrent.tableId;
            }
            return false;
          });

          config.dimensionMenu[menuIndex]
            ? config.dimensionMenu[menuIndex].nodes.push(col.id)
            : config.dimensionMenu[0].nodes.push(col.id);

          config.dimensions[col.id] = {
            type: 'dimension',
            tableId: tableCurrent.tableId,
            field: col.name,
            calculated: false,
            expression: '',
            alias,
            dataType: col.type,
            dataTypeInDB: col.typeInDB,
            isHidden: false,
            renameHash: ''
          } as any;
        } else {
          // 还是老字段，支持更新字段的comment信息
          if (col.comment) {
            if (
              config.dimensions.hasOwnProperty(`${col.id}`) &&
              config.dimensions[`${col.id}`].comment !== col.comment
            ) {
              config.dimensions[`${col.id}`].comment = col.comment;
            } else if (
              config.measures.hasOwnProperty(`${col.id}`) &&
              config.measures[`${col.id}`].comment !== col.comment
            ) {
              config.measures[`${col.id}`].comment = col.comment;
            }
          }
        }
      } else {
        // 度量，表中新增了字段
        if (!config.measures.hasOwnProperty(`${col.id}`) && !config.dimensions.hasOwnProperty(`${col.id}`)) {
          // 防止config.measureMenu为空数组，空数组时进行赋初值

          !config.measureMenu.length
            ? config.measureMenu.push({
                type: 'menu',
                menuId: generateIdUpperCase(true),
                name: tableCurrent.tableName,
                nodes: []
              })
            : null;

          // 将col.id放在对应的measureMenu中
          const menuIndex = config.measureMenu.findIndex(val => {
            if (val.nodes?.length) {
              const dimId = val.nodes[0];
              return config.measures[dimId]?.tableId === tableCurrent.tableId;
            }
            return false;
          });
          config.measureMenu[menuIndex]
            ? config.measureMenu[menuIndex].nodes.push(col.id)
            : config.measureMenu[0].nodes.push(col.id);

          config.measures[col.id] = {
            type: 'measure',
            tableId: tableCurrent.tableId,
            field: col.name,
            calculated: false,
            expression: '',
            alias,
            dataType: col.type,
            dataTypeInDB: col.typeInDB,
            isHidden: false,
            defaultAggregator: 'SUM'
          } as any;
        } else {
          // 还是老字段，支持更新字段的comment信息
          if (col.comment) {
            if (
              config.dimensions.hasOwnProperty(`${col.id}`) &&
              config.dimensions[`${col.id}`].comment !== col.comment
            ) {
              config.dimensions[`${col.id}`].comment = col.comment;
            } else if (
              config.measures.hasOwnProperty(`${col.id}`) &&
              config.measures[`${col.id}`].comment !== col.comment
            ) {
              config.measures[`${col.id}`].comment = col.comment;
            }
          }
        }
      }
    }
  }

  // 同步删除的字段
  config.dimensionMenu.forEach(menu => {
    menu.nodes.forEach((node: string) => {
      if (
        !~dimOrMeaIds.indexOf(node) &&
        !config.dimensions[node]?.calculated &&
        config.dimensions[node]?.type === 'dimension'
      ) {
        removeDimOrMeaIds.push(node);
        // menu.nodes.splice(i--, 1);
        // delete config.dimensions[node];
      }
    });
  });

  config.measureMenu.forEach(menu => {
    menu.nodes.forEach((node: string) => {
      if (
        !~dimOrMeaIds.indexOf(node) &&
        !config.measures[node]?.calculated &&
        config.measures[node]?.type === 'measure'
      ) {
        removeDimOrMeaIds.push(node);
      }
    });
  });

  (config as any).synTable = false;
  (config as any).removeDimOrMeaIds = removeDimOrMeaIds; // 记录删除的字段
  return res.sugarJson({
    hash: dataModelHash,
    name,
    remark: req.body.remark || '',
    config
  });
};

/**
 * 获取数据模型对应的的数据（top 500行）
 */
export const getViewData = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash = req.params.groupHash || '';
  const dataModelHash = req.params.dataModelHash || '';

  const obj = (await req.authorize.do('canUseDataModel', dataModelHash, true)) as IAuthCanUseDataModelReturn;

  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);
  const customTable = await getCustomTablesByHash(dataModelHash);

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('查看数据模型数据失败：API的url错误，空间和数据模型不对应');
  }

  const dataModelSchema: DataModelSchema = dataModel.config as any;
  if (!dataModelSchema.tables || !dataModelSchema.tables.length) {
    throw new CommonError('模型中没有任何表，请先拖拽左侧表至模型中进行建模');
  }

  const dimsMap = dataModelSchema.dimensions;
  const measMap = dataModelSchema.measures;
  const selectDimensions: ISelectDimension[] = [];
  const selectMeasures: ISelectMeasure[] = [];
  const columns: Array<{id: string; node: DataModelSchemaDimension | DataModelSchemaMeasure}> = []; // 最终要返回给前端展示的表格列
  // 查询所有的维度，包括了计算维度
  dataModelSchema.dimensionMenu.forEach(menu => {
    menu.nodes.forEach(nodeId => {
      const dim = dimsMap[nodeId];
      if (dim) {
        if (
          dim.type === 'dimension' &&
          !dim.isHidden &&
          (!dim.calculated ||
            (dim.calculated && !isAggregatedExpression(dim.expression, dim.isAggregated, dataModelSchema)))
        ) {
          columns.push({
            id: nodeId,
            node: dim
          });
          selectDimensions.push({
            id: nodeId,
            frontendId: nodeId,
            type: 'd'
          });
        } else if (dim.type === 'hierarchy' && dim.pathIds && dim.pathIds.length) {
          dim.pathIds.forEach(subNodeId => {
            const subDim = dimsMap[subNodeId];
            if (
              subDim &&
              !subDim.isHidden &&
              (!subDim.calculated ||
                (subDim.calculated && !isAggregatedExpression(subDim.expression, subDim.isAggregated, dataModelSchema)))
            ) {
              columns.push({
                id: subNodeId,
                node: subDim
              });
              selectDimensions.push({
                id: subNodeId,
                frontendId: subNodeId,
                type: 'd'
              });
            }
          });
        }
      }
    });
  });

  // 查询所有的度量，包括了非聚合类的计算度量
  dataModelSchema.measureMenu.forEach(menu => {
    menu.nodes.forEach(nodeId => {
      const mea = measMap[nodeId];
      if (mea) {
        if (
          (!mea.calculated ||
            (mea.calculated &&
              !isAggregatedExpression(mea.expression, mea.isAggregated, dataModelSchema) &&
              !isTableCalExpression(mea.expression, mea.calculatedConfig?.type))) &&
          !mea.isHidden
        ) {
          columns.push({
            id: nodeId,
            node: mea
          });
          selectMeasures.push({
            id: nodeId,
            frontendId: nodeId,
            type: 'm',
            accuracy: mea.format.accuracy
          });
        }
      }
    });
  });

  let sql = '';
  try {
    // const {databases, queryBuilder} = await getDbQueryBuilder(dataModelSchema, obj.group.id);
    // sql = queryBuilder.getSQL({
    //   selectDimensions,
    //   selectMeasures,
    //   customTable,
    //   dataModelSchema,
    //   limit: 300,
    //   notAggregate: true
    // });
    // const database = databases[0];
    // const driver = await getDbDriver(req, {
    //   type: database.type,
    //   database_name: database.database_name,
    //   tunnel_id: database.tunnel_id,
    //   host: database.host,
    //   port: database.port,
    //   username: database.username,
    //   password: database.password,
    //   config: database.config
    // });
    // const dataRows = await driver.runQuery(sql, true);
    // dataTypeFormat(dataRows, dataModelSchema, selectDimensions, selectMeasures, req);
    // await dataRename(dataRows, dataModelSchema, selectDimensions);
    // 将上面注释掉的代码改成了以下的方式来实现，因为要支持跨数据源的数据模型，所有的逻辑都统一在 data-model-proxy.ts 中来实现
    const params: DataModelParams = {
      type: 'data-model-view',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: ([] as any).concat(selectDimensions, selectMeasures),
          limit: 300
        } as any
      }
    };
    const result = await dataModelProxy({...req, notNeedDefaultOrderBy: true}, obj.group, req.user, params, false);
    sql = (result as DataModelProxyReturn).sql || '';
    // status为808是未查询到数据的情况的状态码，和以前的404是一样的
    if (result.status && ![404, 808].includes(result.status)) {
      throw new CommonError(result.msg);
    }
    const dataRows = (result as DataModelProxyReturn).data || [];
    req.log('view_data_model_data', 'notice', {hash: dataModel.hash, sql});
    return res.sugarJson({
      data: {
        columns: columns.map(col => ({
          id: col.id,
          name: col.node.alias,
          type: col.node.type,
          dataType: col.node.convert && col.node.convert.type ? col.node.convert.type : col.node.dataType,
          calculated: col.node.calculated
        })),
        rows: dataRows
      },
      sql
    });
  } catch (e) {
    throw new CommonError(`数据模型预览错误：「${e.message}」`, {sql});
  }
};

/**
 * 获取数据模型中部分数据列对应的的数据（top 500行）
 */
export const getViewDataPartially = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash = req.params.groupHash || '';
  const dataModelHash = req.params.dataModelHash || '';
  const fieldIds: string[] = req.body.fields;
  if (!fieldIds || !fieldIds.length) {
    throw new NotFoundError('未选定字段');
  }

  const obj = (await req.authorize.do('canUseDataModel', dataModelHash, true)) as IAuthCanUseDataModelReturn;

  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);
  const customTable = await getCustomTablesByHash(dataModelHash);

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('查看数据模型数据失败：API的url错误，空间和数据模型不对应');
  }

  const dataModelSchema: DataModelSchema = dataModel.config as any;
  if (!dataModelSchema.tables || !dataModelSchema.tables.length) {
    throw new CommonError('模型中没有任何表，请先拖拽左侧表至模型中进行建模');
  }

  const dimsMap = dataModelSchema.dimensions;
  const measMap = dataModelSchema.measures;
  const selectDimensions: ISelectDimension[] = [];
  const selectMeasures: ISelectMeasure[] = [];
  const columns: Array<{id: string; node: DataModelSchemaDimension | DataModelSchemaMeasure}> = []; // 最终要返回给前端展示的表格列
  // 查询所有的维度，包括了计算维度
  dataModelSchema.dimensionMenu.forEach(menu => {
    menu.nodes.forEach(nodeId => {
      const dim = dimsMap[nodeId];
      if (dim) {
        if (
          dim.type === 'dimension' &&
          !dim.isHidden &&
          (!dim.calculated ||
            (dim.calculated && !isAggregatedExpression(dim.expression, dim.isAggregated, dataModelSchema)))
        ) {
          if (fieldIds.includes(nodeId)) {
            columns.push({
              id: nodeId,
              node: dim
            });
            selectDimensions.push({
              id: nodeId,
              frontendId: nodeId,
              type: 'd'
            });
          }
        } else if (dim.type === 'hierarchy' && dim.pathIds && dim.pathIds.length) {
          dim.pathIds.forEach(subNodeId => {
            const subDim = dimsMap[subNodeId];
            if (subDim && !subDim.isHidden) {
              if (fieldIds.includes(subNodeId)) {
                columns.push({
                  id: subNodeId,
                  node: subDim
                });
                selectDimensions.push({
                  id: subNodeId,
                  frontendId: subNodeId,
                  type: 'd'
                });
              }
            }
          });
        }
      }
    });
  });

  // 查询所有的度量，包括了非聚合类的计算度量
  dataModelSchema.measureMenu.forEach(menu => {
    menu.nodes.forEach(nodeId => {
      const mea = measMap[nodeId];
      if (mea) {
        if (
          (!mea.calculated ||
            (mea.calculated &&
              !isAggregatedExpression(mea.expression, mea.isAggregated, dataModelSchema) &&
              !isTableCalExpression(mea.expression, mea.calculatedConfig?.type))) &&
          !mea.isHidden
        ) {
          if (fieldIds.includes(nodeId)) {
            columns.push({
              id: nodeId,
              node: mea
            });
            selectMeasures.push({
              id: nodeId,
              frontendId: nodeId,
              type: 'm',
              accuracy: mea.format.accuracy
            });
          }
        }
      }
    });
  });

  let sql = '';
  try {
    // const {databases, queryBuilder} = await getDbQueryBuilder(dataModelSchema, obj.group.id);
    // sql = queryBuilder.getSQL({
    //   selectDimensions,
    //   selectMeasures,
    //   customTable,
    //   dataModelSchema,
    //   limit: 300,
    //   notAggregate: true
    // });
    // const database = databases[0];
    // const driver = await getDbDriver(req, {
    //   type: database.type,
    //   database_name: database.database_name,
    //   tunnel_id: database.tunnel_id,
    //   host: database.host,
    //   port: database.port,
    //   username: database.username,
    //   password: database.password,
    //   config: database.config
    // });
    // const dataRows = await driver.runQuery(sql, true);
    // dataTypeFormat(dataRows, dataModelSchema, selectDimensions, selectMeasures, req);
    // await dataRename(dataRows, dataModelSchema, selectDimensions);
    // 将上面注释掉的代码改成了以下的方式来实现，因为要支持跨数据源的数据模型，所有的逻辑都统一在 data-model-proxy.ts 中来实现
    const params: DataModelParams = {
      type: 'data-model-view',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: ([] as any).concat(selectDimensions, selectMeasures),
          limit: 300
        } as any
      }
    };
    const result = await dataModelProxy({...req, notNeedDefaultOrderBy: true}, obj.group, req.user, params, false);
    sql = (result as DataModelProxyReturn).sql || '';
    // status为808是未查询到数据的情况的状态码，和以前的404是一样的
    if (result.status && ![404, 808].includes(result.status)) {
      throw new CommonError(result.msg);
    }
    const dataRows = (result as DataModelProxyReturn).data || [];
    req.log('view_data_model_data_partially', 'notice', {hash: dataModel.hash, sql, fieldIds});
    return res.sugarJson({
      data: {
        columns: columns.map(col => ({
          id: col.id,
          name: col.node.alias,
          type: col.node.type,
          dataType: col.node.convert && col.node.convert.type ? col.node.convert.type : col.node.dataType,
          calculated: col.node.calculated
        })),
        rows: dataRows
      },
      sql
    });
  } catch (e) {
    throw new CommonError(`数据模型预览错误：「${e.message}」`, {sql});
  }
};

/**
 * 获取某字段的统计信息
 */
export const getFieldStatistic = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash = req.params.groupHash || '';
  const dataModelHash = req.params.dataModelHash || '';
  const fieldId: string = req.query.field;
  if (!fieldId) {
    throw new NotFoundError('未选定字段');
  }

  const obj = (await req.authorize.do('canUseDataModel', dataModelHash, true)) as IAuthCanUseDataModelReturn;

  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);
  const customTable = await getCustomTablesByHash(dataModelHash);

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('查看数据模型数据失败：API的url错误，空间和数据模型不对应');
  }

  const dataModelSchema: DataModelSchema = dataModel.config as any;
  if (!dataModelSchema.tables || !dataModelSchema.tables.length) {
    throw new CommonError('模型中没有任何表，请先拖拽左侧表至模型中进行建模');
  }

  const dimsMap = dataModelSchema.dimensions;
  const measMap = dataModelSchema.measures;
  let result: any = {
    data: [],
    chart: []
  };
  let field: DataModelSchemaDimension | DataModelSchemaMeasure = dimsMap[fieldId];
  if (!field) {
    field = measMap[fieldId];
  }
  if (!field) {
    throw new CommonError('字段不存在');
  }
  let finalDataType;
  let needMinMax = false;
  let needDistinctCount = false;
  let needTimeLine = false;
  let needAvg = false;
  let dataMin = null;
  let dataMax = null;
  result.name = field.alias || field.field;
  if (field.type === 'dimension') {
    finalDataType = field.convert.type || field.dataType;
    if (finalDataType !== 'string') {
      needMinMax = true;
    } else {
      needDistinctCount = true;
    }
    if (finalDataType === 'float' || finalDataType === 'int') {
      needDistinctCount = true;
    }
    if (finalDataType === 'date' || finalDataType === 'datetime') {
      needTimeLine = true;
    }
    result.type = finalDataType;
  } else {
    finalDataType = field.convert.type || field.dataType;
    if (finalDataType !== 'string') {
      needMinMax = true;
    } else {
      needDistinctCount = true;
    }
    if (finalDataType === 'float' || finalDataType === 'int') {
      needAvg = true;
    }
    result.type = finalDataType;
  }

  if (needMinMax) {
    try {
      let min = await getStatisticData(req, dataModel, customTable, obj.group, {
        dataModel: {
          x: [
            {
              frontendId: 'SDATA', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              sort: 'asc'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true, // 获取所有的维度取值，不加任何where限制
        tableNotAggregate: true
      });
      let finalMin = min.SDATA;
      if (['date', 'datetime', 'timestamp'].includes(finalDataType)) {
        finalMin = +new Date(min.SDATA);
        if (finalDataType === 'date') {
          finalMin = formatDate(finalMin);
        } else {
          finalMin = formatDate(finalMin, '$1-$2-$3 $4:$5:$6');
        }
      }
      result.data.push({type: 'min', data: finalMin});
      dataMin = finalMin;
    } catch (error) {
      result.data.push({type: 'min', error: error.message});
    }
    try {
      let max = await getStatisticData(req, dataModel, customTable, obj.group, {
        dataModel: {
          x: [
            {
              frontendId: 'SDATA', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              sort: 'desc'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true, // 获取所有的维度取值，不加任何where限制
        tableNotAggregate: true
      });
      let finalMax = max.SDATA;
      if (['date', 'datetime', 'timestamp'].includes(finalDataType)) {
        finalMax = +new Date(max.SDATA);
        if (finalDataType === 'date') {
          finalMax = formatDate(finalMax);
        } else {
          finalMax = formatDate(finalMax, '$1-$2-$3 $4:$5:$6');
        }
      }
      result.data.push({type: 'max', data: finalMax});
      dataMax = finalMax;
    } catch (error) {
      result.data.push({type: 'max', error: error.message});
    }
  }
  if (needAvg) {
    try {
      let avg = await getStatisticData(req, dataModel, customTable, obj.group, {
        dataModel: {
          x: [
            {
              frontendId: 'SDATA', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              aggregator: 'AVG'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true
      });
      if (finalDataType !== 'float') {
        result.data.push({type: 'avg', data: (+avg.SDATA).toFixed(2)});
      } else {
        result.data.push({type: 'avg', data: (+avg.SDATA).toFixed(4)});
      }
    } catch (error) {
      result.data.push({type: 'avg', error: error.message});
    }
    try {
      let std = await getStatisticData(req, dataModel, customTable, obj.group, {
        dataModel: {
          x: [
            {
              frontendId: 'SSTD', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              aggregator: 'STD'
            },
            {
              frontendId: 'SVAR', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              aggregator: 'VAR'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true
      });
      if (finalDataType !== 'float') {
        result.data.push({type: 'std', data: (+std.SSTD).toFixed(2)});
        result.data.push({type: 'var', data: (+std.SVAR).toFixed(2)});
      } else {
        result.data.push({type: 'std', data: (+std.SSTD).toFixed(4)});
        result.data.push({type: 'var', data: (+std.SVAR).toFixed(4)});
      }
    } catch (error) {
      result.data.push({type: 'std', error: error.message});
    }
  }
  if (needAvg) {
    // 自动计算直方图的步长
    let interval = 10;
    if (dataMin !== null && dataMax !== null && !isNaN(dataMin) && !isNaN(dataMax)) {
      let diff = dataMax - dataMin;
      if (diff >= 10) {
        interval = Math.max(1, Math.round(diff / 10));
      } else if (diff > 5) {
        interval = 1;
      } else if (diff >= 2) {
        interval = 0.5;
      } else {
        interval = diff / 10;
      }
    }
    // 直方图
    let params: DataModelParams = {
      type: 'histogram',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'SDATAX', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0]
              // sort: 'asc'
            }
          ],
          y: [
            {
              frontendId: 'SDATAY', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: 'sugar_count*',
              type: 'm'
            }
          ]
        } as any,
        calculatedConfig: {
          mode: 'fixed',
          otherName: '其它',
          type: 'bin',
          value: interval,
          completion: false
        },
        notNeedWhere: true
      }
    };
    try {
      const data = await dataModelProxy(
        {
          ...req,
          ignoreDataLineLimits: true, // 忽略行级别权限的限制
          ignoreDataFuzzy: true, // 不进行数据脱敏处理
          notNeedDefaultOrderBy: true
        },
        obj.group,
        null,
        params,
        false
      );
      if (data.status && ![404, 808].includes(data.status)) {
        throw new CommonError(data.msg);
      }
      if (!data.dataset || !data.dataset[0]) {
        throw new CommonError('未查到数据');
      }
      result.chart.push({type: 'histogram', data: data.data});
    } catch (error) {
      result.chart.push({type: 'histogram', error: error.message});
    }
  }

  if (needTimeLine) {
    let range = 0;
    if (dataMax && dataMin) {
      range = (+new Date(dataMax) - +new Date(dataMin)) / 1000;
    }
    let cusDefaultVal = 'YEAR-MONTH-DAY';
    if (finalDataType === 'datetime') {
      cusDefaultVal = 'SECOND';
      // 大于 31 天
      if (range > 3600 * 24 * 31) {
        cusDefaultVal = 'YEAR-MONTH-DAY';
      } else if (range > 3600 * 24 * 5) {
        // 大于 5 天
        cusDefaultVal = 'YEAR-MONTH-DAY-HOUR';
      } else if (range > 12 * 3600) {
        // 大于 12 小时
        cusDefaultVal = 'YEAR-MONTH-DAY-HOUR-MINUTE';
      }
    }

    range = range / 3600 / 24;
    if (range > 365 * 10) {
      cusDefaultVal = 'YEAR';
    } else if (range > 365 * 5) {
      cusDefaultVal = 'YEAR-QUARTER';
    } else if (range > 365) {
      cusDefaultVal = 'YEAR-MONTH';
    } else if (range > 180) {
      cusDefaultVal = 'YEAR-WEEK';
    }
    // 折线图
    let params: DataModelParams = {
      type: 'line',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'SDATAX', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              dateTimeType: cusDefaultVal
              // sort: 'asc'
            }
          ],
          y: [
            {
              frontendId: 'SDATAY', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: 'sugar_count*',
              type: 'm'
            }
          ]
        } as any,
        notNeedWhere: true
      }
    };
    try {
      const data = await dataModelProxy(
        {
          ...req,
          ignoreDataLineLimits: true, // 忽略行级别权限的限制
          ignoreDataFuzzy: true, // 不进行数据脱敏处理
          notNeedDefaultOrderBy: true
        },
        obj.group,
        null,
        params,
        false
      );
      if (data.status && ![404, 808].includes(data.status)) {
        throw new CommonError(data.msg);
      }
      if (!data.dataset || !data.dataset[0]) {
        throw new CommonError('未查到数据');
      }
      result.chart.push({type: 'line', data: data.data});
    } catch (error) {
      result.chart.push({type: 'line', error: error.message});
    }
  }

  if (needDistinctCount) {
    try {
      let count = await getStatisticData(req, dataModel, customTable, obj.group, {
        dataModel: {
          x: [
            {
              frontendId: 'SDATA', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0],
              aggregator: 'COUNTD'
            }
          ],
          limit: 1
        } as any,
        notNeedWhere: true
      });
      result.data.push({type: 'count', data: count.SDATA});
    } catch (error) {
      result.data.push({type: 'count', error: error.message});
    }
  }
  if (needDistinctCount) {
    // 柱图
    let params: DataModelParams = {
      type: 'bar',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          x: [
            {
              frontendId: 'SDATAX', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0]
            }
          ],
          y: [
            {
              frontendId: 'SDATAY', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: 'sugar_count*',
              type: 'm',
              sort: 'desc'
            }
          ]
        } as any,
        notNeedWhere: true
      }
    };
    try {
      const data = await dataModelProxy(
        {
          ...req,
          ignoreDataLineLimits: true, // 忽略行级别权限的限制
          ignoreDataFuzzy: true, // 不进行数据脱敏处理
          notNeedDefaultOrderBy: true
        },
        obj.group,
        null,
        params,
        false
      );
      if (data.status && ![404, 808].includes(data.status)) {
        throw new CommonError(data.msg);
      }
      if (!data.dataset || !data.dataset[0]) {
        throw new CommonError('未查到数据');
      }
      result.chart.push({type: 'bar', data: data.data});
    } catch (error) {
      result.chart.push({type: 'bar', error: error.message});
    }
    // 饼图
    params = {
      type: 'pie',
      dataModelHash: dataModel.hash,
      dataModel,
      customTable,
      dataConfig: {
        dataModel: {
          color: [
            {
              frontendId: 'SDATAX', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: fieldId,
              type: field.type[0]
            }
          ],
          size: [
            {
              frontendId: 'SDATAY', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
              id: 'sugar_count*',
              type: 'm',
              sort: 'desc'
            }
          ]
        } as any,
        notNeedWhere: true
      }
    };
    try {
      const data = await dataModelProxy(
        {
          ...req,
          ignoreDataLineLimits: true, // 忽略行级别权限的限制
          ignoreDataFuzzy: true, // 不进行数据脱敏处理
          notNeedDefaultOrderBy: true
        },
        obj.group,
        null,
        params,
        false
      );
      if (data.status && ![404, 808].includes(data.status)) {
        throw new CommonError(data.msg);
      }
      if (!data.dataset || !data.dataset[0]) {
        throw new CommonError('未查到数据');
      }
      result.chart.push({type: 'pie', data: data.data});
    } catch (error) {
      result.chart.push({type: 'pie', error: error.message});
    }
  }

  res.sugarJson(result);
};

// 查询单一统计值工具函数
const getStatisticData = async (
  req: Request,
  dataModel: IDataModelInstance,
  customTable: ICustomTableInstance[],
  group: IGroupInstance,
  dataConfig: IDataConfig
) => {
  const params: DataModelParams = {
    type: 'dimension_select',
    dataModelHash: dataModel.hash,
    dataModel,
    customTable,
    dataConfig
  };
  const data = await dataModelProxy(
    {
      ...req,
      ignoreDataLineLimits: true, // 忽略行级别权限的限制
      ignoreDataFuzzy: true, // 不进行数据脱敏处理
      notNeedDefaultOrderBy: true
    },
    group,
    null,
    params,
    false
  );
  // status为808是未查询到数据的情况的状态码，和以前的404是一样的
  if (data.status && ![404, 808].includes(data.status)) {
    throw new CommonError(data.msg);
  }
  if (!data.dataset || !data.dataset[0]) {
    throw new CommonError('未查到数据');
  }
  return data.dataset[0];
};

/**
 * 获取某数据模型的字段文件夹结构
 * @param req Request
 * @param res Response
 * @param next NextFunction
 */
export const getFields = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash = req.params.groupHash || '';
  const dataModelHash = req.params.dataModelHash || '';
  const onlyDimension = req.query.onlyDimension || '';
  const onlyMeasure = req.query.onlyMeasure || '';
  const withAllValue = !!req.query.withAllValue;
  const disableFolder = !req.query.canSelectFolder;
  const disableAggrTableCal = !req.query.canSelectAggrTableCal;
  const disablePredict = !req.query.canSelectPredict;
  let reportOrDashboardHash = req.query.reportOrDashboardHash || '';
  let needCount = !!req.query.needCount || false; // 是否需要返回记录数
  let chartHash: string = req.query.chartHash || '';

  let obj;
  let filterFields: string[] = [];
  if (chartHash) {
    // 如果传入了 chartHash，看看有没有图表浏览权限
    let chartType = 'report';
    if (chartHash.startsWith('d-')) {
      chartHash = chartHash.replace('d-', '');
      chartType = 'dashboard';
    }
    obj = await req.authorize.canReadChart(chartHash, chartType as any);
    if (obj === false) {
      throw new CommonError('您还没有使用这个图表数据的权限，请联系管理员。');
    }
    // 需要页面级计算字段
    reportOrDashboardHash = chartType === 'report' ? obj.report?.hash || '' : obj.dashboard?.hash || '';
    //
    // 看看可分析的字段有哪些
    let analyzeFields = getUsingFieldsFromDataConfig(obj.chart.data_config);
    // 使用记录数的时候需要返回记录数的选择
    needCount = analyzeFields.m.includes('sugar_count*');
    filterFields = [...analyzeFields.d, ...analyzeFields.m];
  } else {
    // 有数据源的编辑和可视化分析权限的都可以拉取数据模型字段列表
    obj = await req.authorize.canUseDataModel(dataModelHash);
    if (obj === false) {
      obj = await req.authorize.canEditDataModel(dataModelHash);
    }

    if (obj === false) {
      throw new CommonError('您还没有使用这个数据模型的权限，请联系管理员。');
    }
  }

  const dataModel = await getDataModelByHash(dataModelHash);

  if (!dataModel) {
    throw new NotFoundError('没有找到该数据模型，可能已被删除！');
  }

  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('获取数据模型信息失败：API的url错误，空间和数据模型不对应');
  }
  // @ts-ignore
  const config: DataModelSchema = dataModel.config;
  if (needCount && config.measures) {
    config.measures['sugar_count*'] = sugar_count;
    config.measureMenu.push({
      type: 'menu',
      name: sugar_t(req, '记录数'),
      menuId: 'sugar_count*_menu',
      nodes: ['sugar_count*']
    });
  }

  let fieldType = req.query.fieldType || '';
  let fieldTypeArr;
  if (fieldType) {
    fieldTypeArr = fieldType.split(',');
  }

  const pageData = await reportOrDashboard(reportOrDashboardHash, dataModelHash);

  let ret: any[] = [];
  if (config.dimensionMenu && config.dimensionMenu.length && !onlyMeasure) {
    let dimTop: PlainObject = {
      label: sugar_t(req, '维度'),
      value: 'dim',
      icon: 'fa fa-folder-open',
      disabled: disableFolder,
      children: [].concat(
        getFieldTree(
          config.dimensionMenu,
          config.dimensions,
          fieldTypeArr,
          disableFolder,
          disableAggrTableCal,
          disablePredict
        ),
        pageData
          ? getFieldTree(
              pageData.dimensionMenu,
              pageData.dimensions,
              fieldTypeArr,
              disableFolder,
              disableAggrTableCal,
              disablePredict
            )
          : []
      )
    };
    ret.push(dimTop);
  }
  if (config.measureMenu && config.measureMenu.length && !onlyDimension) {
    let meaTop: PlainObject = {
      label: sugar_t(req, '度量'),
      value: 'mea',
      icon: 'fa fa-folder-open',
      disabled: disableFolder,
      children: [].concat(
        getFieldTree(
          config.measureMenu,
          config.measures,
          fieldTypeArr,
          disableFolder,
          disableAggrTableCal,
          disablePredict
        ),
        pageData
          ? getFieldTree(
              pageData.measureMenu,
              pageData.measures,
              fieldTypeArr,
              disableFolder,
              disableAggrTableCal,
              disablePredict
            )
          : []
      )
    };
    ret.push(meaTop);
  }

  if (chartHash && filterFields.length) {
    ret = filterTree(ret, filterFields);
  }

  let finalRet: any = {
    options: ret
  };

  if (withAllValue) {
    let value: string[] = [];
    let stack: any[] = [...ret];
    while (stack.length) {
      let now = stack.shift();
      value.push(now.value);
      if (now.children && now.children.length) {
        stack.push(...now.children);
      }
    }
    finalRet.value = value.join(',');
  }

  return res.sugarJson(finalRet);
};

// 根据列表过滤下节点，去掉文件夹结构
function filterTree(ret: any, needFields: string[]) {
  let retFiltered: any = [];
  let stack: any[] = [...ret];
  while (stack.length) {
    let now = stack.shift();
    if (needFields.includes(now.value)) {
      retFiltered.push({
        value: now.value,
        label: now.label
      });
    }
    if (now.children && now.children.length) {
      stack.push(...now.children);
    }
  }
  return retFiltered;
}

function getFieldTree(
  menus: DataModelSchemaMenu[],
  map: {[id: string]: DataModelSchemaDimension | DataModelSchemaMeasure},
  fieldTypeArr?: string[],
  disableFolder: boolean = true,
  disableAggrTableCal: boolean = true,
  disablePredict: boolean = true
) {
  const res: any = [];
  for (const menu of menus) {
    if (menu.type === 'menu') {
      // 预测字段被禁止直接不展示
      if (disablePredict && menu.predictType) {
        continue;
      }
      const menuNode: PlainObject = {
        label: menu.name,
        value: menu.menuId,
        disabled: disableFolder,
        icon: `${menu.predictType ? 'fab fa-monero' : 'fa fa-folder-open'}`,
        children: []
      };
      if (menu.nodes && menu.nodes.length) {
        for (const node of menu.nodes) {
          const nodeDetail = map[node];
          if (nodeDetail) {
            if ((nodeDetail.type === 'dimension' || nodeDetail.type === 'measure') && !nodeDetail.isHidden) {
              let fieldType = nodeDetail.convert.type || nodeDetail.dataType;
              let obj: PlainObject = {
                label: nodeDetail.alias,
                value: node,
                icon: `sugar-icon icon-${nodeDetail.calculated ? 'cal' : fieldType}`
              };
              if (fieldTypeArr && fieldTypeArr.length) {
                if (!fieldTypeArr.includes(fieldType)) {
                  obj.disabled = true;
                }
              }
              // 聚合字段设置为禁用
              const aggregated = isAggregatedExpression(nodeDetail.expression, nodeDetail.isAggregated);
              if (
                disableAggrTableCal &&
                (aggregated || isTableCalExpression(nodeDetail.expression, nodeDetail.calculatedConfig?.type))
              ) {
                obj.disabled = true;
                obj.icon = `sugar-icon icon-${aggregated ? 'juhe' : 'cal'}`;
              }

              if (!obj.disabled) {
                obj.icon += ` text-${nodeDetail.type}`;
              }

              menuNode.children.push(obj);
            } else if (nodeDetail.type === 'hierarchy') {
              const hierarchyNode: PlainObject = {
                label: nodeDetail.alias,
                value: node,
                disabled: disableFolder,
                icon: 'sugar-icon icon-hierarchy',
                children: []
              };
              if (nodeDetail.pathIds && nodeDetail.pathIds.length) {
                for (const hNode of nodeDetail.pathIds) {
                  const hNodeDetail = map[hNode];
                  if (
                    hNodeDetail &&
                    (hNodeDetail.type === 'dimension' || hNodeDetail.type === 'measure') &&
                    !hNodeDetail.isHidden
                  ) {
                    const obj: PlainObject = {
                      label: hNodeDetail.alias,
                      value: hNode,
                      icon: 'sugar-icon icon-' + hNodeDetail.dataType
                    };
                    if (fieldTypeArr && fieldTypeArr.length) {
                      const fieldType = hNodeDetail.convert.type || hNodeDetail.dataType;
                      if (!fieldTypeArr.includes(fieldType)) {
                        obj.disabled = true;
                      }
                    }
                    if (!obj.disabled) {
                      obj.icon += ' text-dimension';
                    }
                    hierarchyNode.children.push(obj);
                  }
                }
              }
              menuNode.children.push(hierarchyNode);
            }
          }
        }
      }
      res.push(menuNode);
    }
  }
  return res;
}

/**
 * 获取某一个数据模型中每个string类型维度的去重取值（只取了最多1000条）
 * 目前本API只是用来测试NLP那块 `server/cron/data-model-stat-task.ts` 中 `getDimDistinctData` 方法的正确性，方便那块代码做修改时的实时测试
 * 后续在数据模型的智能自动分析中，应该会有用
 * @param req
 * @param res
 * @param next
 */
export const getModelDimsDistinctData = async (req: Request, res: Response, next: NextFunction) => {
  const groupHash: string = req.params.groupHash;
  const dataModelHash: string = req.params.dataModelHash;
  // 能够编辑该数据模型，才能调用该API
  const obj = await req.authorize.canEditDataModel(dataModelHash, true);
  if (!obj) {
    throw new AuthorizeError('您无权限查看该数据模型');
  }
  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);
  if (dataModel.group_id !== obj.group.id) {
    throw new CommonError('获取数据模型信息失败：API的url错误，空间和数据模型不对应');
  }
  const ret = await getDimDistinctData(dataModel, req.company, obj.group);
  res.sugarJson(ret);
};

/**
 * 通过维度获取维度相关值
 * @param req
 * @param res
 * @param next
 */
export const getSelectDimensionsValue = async (req: Request, res: Response, next: NextFunction) => {
  const {dataModelHash = '', reportOrDashboardHash = '', dimensionsId, searchText} = req.query;
  const filters: any[] = req.body?.filters || [];
  const isNeedSearchAll: boolean = req.body?.isNeedSearchAll || false; // 是否在进行关键词搜索时进行全量搜索，在首次加载的时候只需要展示过滤条件影响下的数据，在搜索时去进行全量搜索忽视过滤条件的影响
  // 数据模型的使用权限才可以拉取字段
  const obj = (await req.authorize.do('canUseDataModel', dataModelHash)) as IAuthCanUseDataModelReturn;
  await getSelectDimensionsValueCommon(
    req,
    res,
    next,
    dataModelHash,
    reportOrDashboardHash,
    dimensionsId,
    searchText,
    filters,
    isNeedSearchAll,
    obj
  );
};

/**
 * 报表消费通过维度获取维度相关值
 * @param req
 * @param res
 * @param next
 */
export const getSelectDimensionsValueConsume = async (req: Request, res: Response, next: NextFunction) => {
  const {dataModelHash = '', reportOrDashboardHash = '', dimensionsId, searchText} = req.query;
  const filters: any[] = req.body?.filters || [];
  const isNeedSearchAll: boolean = req.body?.isNeedSearchAll || false; // 是否在进行关键词搜索时进行全量搜索，在首次加载的时候只需要展示过滤条件影响下的数据，在搜索时去进行全量搜索忽视过滤条件的影响
  // 只要有报表的AI分析权限，就可以拉取数据模型的字段
  const obj = (await req.authorize.canAIAnalysis(reportOrDashboardHash)) as IAuthCanAIAnalysisReturn;
  await getSelectDimensionsValueCommon(
    req,
    res,
    next,
    dataModelHash,
    reportOrDashboardHash,
    dimensionsId,
    searchText,
    filters,
    isNeedSearchAll,
    obj
  );
};

/**
 * 智能问数通过维度获取维度相关值
 * @param req
 * @param res
 * @param next
 */
export const getSelectDimensionsValueGBI = async (req: Request, res: Response, next: NextFunction) => {
  const {dataModelHash = '', dimensionsId, searchText} = req.query;
  const filters: any[] = req.body?.filters || [];
  const isNeedSearchAll: boolean = req.body?.isNeedSearchAll || false; // 是否在进行关键词搜索时进行全量搜索，在首次加载的时候只需要展示过滤条件影响下的数据，在搜索时去进行全量搜索忽视过滤条件的影响

  // 有数据模型的问数权限，就可以拉取数据
  const obj = await req.authorize.canUseDMLearn(dataModelHash, 2);
  if (!obj) {
    throw new CommonError('无权限');
  }

  await getSelectDimensionsValueCommon(
    req,
    res,
    next,
    dataModelHash,
    '',
    dimensionsId,
    searchText,
    filters,
    isNeedSearchAll,
    obj
  );
};

/**
 * 智能问数iframe通过维度获取维度相关值
 * @param req
 * @param res
 * @param next
 */
export const getSelectDimensionsValueGBIIframe = async (req: Request, res: Response, next: NextFunction) => {
  const {dataModelHash = '', dimensionsId, searchText} = req.query;
  const shareId = req.body._sugar_share_id;
  const filters: any[] = req.body?.filters || [];
  const isNeedSearchAll: boolean = req.body?.isNeedSearchAll || false; // 是否在进行关键词搜索时进行全量搜索，在首次加载的时候只需要展示过滤条件影响下的数据，在搜索时去进行全量搜索忽视过滤条件的影响

  const authDataModels = await canUseDMLearns([dataModelHash], {
    user: req.user,
    company: req.company,
    checkByHash: true,
    shareId
  });
  if (!authDataModels.length) {
    throw new CommonError('无权限');
  }
  const obj = authDataModels[0];

  await getSelectDimensionsValueCommon(
    req,
    res,
    next,
    dataModelHash,
    '',
    dimensionsId,
    searchText,
    filters,
    isNeedSearchAll,
    obj
  );
};

/**
 * 获取维度相关值公共方法
 * @param req
 * @param res
 * @param next
 * @param dataModelHash
 * @param reportOrDashboardHash
 * @param dimensionsId
 * @param searchText
 * @param filters
 * @param isNeedSearchAll
 * @param obj
 */
const getSelectDimensionsValueCommon = async (
  req: Request,
  res: Response,
  next: NextFunction,
  dataModelHash: string,
  reportOrDashboardHash: string,
  dimensionsId: string,
  searchText: string,
  filters: any[],
  isNeedSearchAll: boolean,
  obj: PlainObject
) => {
  (req as any).ignoreDataLineLimits = true; // 忽略行级别权限的限制
  (req as any).ignoreDataFuzzy = true; // 不进行数据脱敏处理

  // 获取报表和大屏中自行创建的字段的数据模型信息
  const pageData = await reportOrDashboard(reportOrDashboardHash, dataModelHash);

  const filtersTmp: Array<{filter: DataModelSchemaFilterDetail}> = [];
  // 在首次加载的时候只需要展示过滤条件影响下的数据，在搜索时去进行全量搜索忽视过滤条件的影响
  if (filters?.length && (!isNeedSearchAll || (!searchText && isNeedSearchAll))) {
    // amis传递数据时$字符信息都会替换成空字符串导致maxDate和minDate为空，前端去掉$字符，此处再加回来
    filters.forEach((item: {filter: DataModelSchemaFilterDetail}) => {
      if (item?.filter?.searchParams.type == 'date') {
        const {minDate, maxDate} = item?.filter?.searchParams.date.detail;
        if (minDate?.indexOf('$') == -1 || maxDate?.indexOf('$') == -1) {
          item.filter.searchParams.date.detail = {
            minDate: `$${minDate}`,
            maxDate: `$${maxDate}`
          };
        }
      }
      filtersTmp.push(item);
    });
  }
  // searchText主要是用在远程搜索的场景，比如输入关键字，然后通过关键字进行模糊匹配
  if (searchText) {
    filtersTmp.push({
      filter: {
        id: dimensionsId,
        searchParams: {
          type: 'string',
          textMatch: {
            operator: 'AND',
            value: [{type: 'include', value: searchText}]
          }
        } as any,
        connect: 'AND'
      } as any
    });
  }
  const rest = {
    type: 'dimension_select',
    dataModelHash: dataModelHash,
    dataConfig: {
      dataModel: {
        x: [
          {
            frontendId: 'DIMVAL', // frontendId必须都大写，oracle返回的结果中字段名都是大写。并且在javakit项目中我们已经将所有查询结果的字段名都转成大写了
            id: dimensionsId,
            type: 'd',
            sort: 'asc'
          }
        ],
        filters: {
          connect: 'AND',
          exprs: filtersTmp || []
        },
        limit: 3000 // 最多返回3000行数据，数据太多会导致前端select组件卡死，数据太多建议使用「手动」输入的方式。 1000太少，改成3000试试
      },
      notNeedWhere: !filtersTmp?.length // 获取所有的纬度值
    },
    pageData: pageData || {}
  };
  return getDataByModel(req, res, obj.group, req.user || null, rest as any, false, {}, {}, [], undefined, false, true);
};

/**
 * 获取报表或者大屏的数据模型信息
 * @param reportOrDashboardHash 报表或者大屏hash
 * @param dataModelHash 数据模型hash
 * @returns pageData 页面级别计算字段
 */
export const reportOrDashboard = async (reportOrDashboardHash: string, dataModelHash: string) => {
  let pageData;
  let reportOrDashboard: IReportInstance | IDashboardInstance | null = await Report.findOne({
    where: {
      hash: reportOrDashboardHash
    },
    include: ['group'] as any
  });

  if (reportOrDashboard) {
    const reportDatamodelConfig = await getReportDatamodelConfig(reportOrDashboard.datamodel_config as string);
    pageData =
      (reportDatamodelConfig as PlainObject)?.dataModelDetailMap &&
      (reportDatamodelConfig as PlainObject)?.dataModelDetailMap[dataModelHash];
  } else {
    reportOrDashboard = await Dashboard.findOne({
      where: {
        hash: reportOrDashboardHash
      },
      include: ['group'] as any
    });
    if (reportOrDashboard) {
      const dashboardDatamodelConfig = await getDashboardDatamodelConfig(reportOrDashboard.datamodel_config as string);
      pageData =
        (dashboardDatamodelConfig as PlainObject)?.dataModelDetailMap &&
        (dashboardDatamodelConfig as PlainObject)?.dataModelDetailMap[dataModelHash];
    }
  }
  return pageData as DataModelSchema;
};

/**
 * 根据数据模型 hash 获取数据模型使用的数据源 type
 * @param req
 * @param res
 * @param next
 */
export const sqlFuncDemo = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.query.dataModelHash || '';
  const obj = (await req.authorize.do('canUseDataModel', dataModelHash, true)) as IAuthCanUseDataModelReturn;
  const dataModel = obj.dataModel;
  await getConfigOfDataModel(dataModel);

  let types: number[] = [],
    sqlFuncDemo = {};

  if (dataModel && dataModel.group_id === obj.group.id) {
    const databases = await Database.findAll({
      where: {
        group_id: obj.group.id,
        hash: {
          $in: dataModel.database_hashes.split(',')
        }
      }
    });
    // 当找不到对应的数据源时查一下是不是内置数据源
    if (databases.length) {
      databases.forEach(database => {
        types.push(database.type);
      });
      types = uniq(types);
      sqlFuncDemo = types?.length ? sqlFuncDemos[types[0]] : {};
    } else if (dataModel.database_hashes === yog.conf.sugar.sugarExampleDatabase) {
      // database_hashes为 yog.conf.sugar.sugarExampleDatabase 说明为内置数据源
      sqlFuncDemo = sqlFuncDemos[0];
    }
  }
  res.sugarJson({type: types[0], sqlFuncDemo});
};

/**
 * 根据数据模型 hash 获取数据模型维度度量
 * @param req
 * @param res
 * @param next
 */
export const getModelSchemaByHash = async (req: Request, res: Response, next: NextFunction) => {
  const dataModelHash = req.body.dataModelHash;

  if (!Array.isArray(dataModelHash)) {
    throw new CommonError('参数错误');
  }

  // 获取有权限的数据模型
  const authDataModels = await canUseDMLearns(dataModelHash, {
    user: req.user,
    company: req.company,
    checkByHash: true
  });
  const authDataModelHashs = authDataModels.filter(item => !!item).map(item => item.hash);

  if (authDataModelHashs.length === 0) {
    return res.sugarJson(dataModelHash.map(hash => ({canUse: false, dataModelHash: hash})));
  }

  const dataModels = await getDataModelsByWhere({hash: authDataModelHashs});
  Promise.all(
    dataModels.map(async model => {
      const {columns} = await processDataModelSchema(model, false);
      const learnDataModel = authDataModels.find(item => item.hash === model.hash);
      const transferColumns = columns.map(item => ({
        bizType: item.bizType,
        alias: item.alias,
        remark: item.remark,
        dataType: item.type
      }));

      return {
        name: learnDataModel?.dmLearn?.name || '',
        dataModelHash: model.hash,
        remark: learnDataModel?.dmLearn?.remark || '',
        dimensions: transferColumns.filter(item => item.bizType === 'dimension'),
        measures: transferColumns.filter(item => item.bizType === 'measure'),
        canUse: true
      };
    })
  )
    .then(models => {
      res.sugarJson(
        dataModelHash.map(hash => {
          const authModel = models.find(model => model.dataModelHash === hash);
          if (authModel) {
            return {...authModel, canUse: true};
          }
          return {dataModelHash: hash, canUse: false};
        })
      );
    })
    .catch(() => {
      res.sugarErrorJson('获取模型维度度量失败');
    });
};
