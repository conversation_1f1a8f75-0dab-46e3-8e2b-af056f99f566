/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-30
 * @Description: 重新生成总结接口
 */

import {Request, Response, NextFunction} from '../../lib/types';
import {CommonError} from '../../lib/errors';
import axios from 'axios';

const plannerHost = process.env.sugar_gbi_planner_endpoint;

// 调试开关：设置为true使用本地mock服务器，false使用真实planner服务器
const USE_MOCK_SERVER = true;
const MOCK_PLANNER_HOST = 'http://localhost:3001';

export interface IRegenerateSummaryRequest {
  msgId: string;
  dataList: Array<{
    dataModelHash: string;
    llmJson: any;
    chartData: any;
  }>;
}

export interface IPlannerSummaryRequest {
  dataList: Array<{
    llmJson: any;
    dataModelHash: string;
    chartData: any;
  }>;
  queryMsgId: string | number;
}

export interface IRegenerateSummaryResponse {
  status: number;
  message: string;
  data?: {
    key: string;
  };
}

/**
 * 重新生成总结请求接口
 * @param req 
 * @param res 
 * @param next 
 */
export const regenerateSummary = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🚀 收到重新生成总结请求');
    const {msgId, dataList}: IRegenerateSummaryRequest = req.body;
    console.log(`📋 请求参数 - msgId: ${msgId}, dataList长度: ${dataList?.length || 0}`);

    if (!msgId || !dataList || !Array.isArray(dataList) || dataList.length === 0) {
      return res.sugarErrorJson('参数错误：msgId和dataList不能为空');
    }

    if (!plannerHost) {
      throw new CommonError('未配置planner地址');
    }

    // 构建发送给planner的请求数据，按照实际接口格式
    const plannerRequestData: IPlannerSummaryRequest = {
      dataList: dataList.map(item => ({
        llmJson: item.llmJson,
        dataModelHash: item.dataModelHash,
        chartData: item.chartData
      })),
      queryMsgId: msgId
    };

    // 调用planner接口重新生成总结
    // 根据调试开关选择使用mock服务器还是真实服务器
    const targetHost = USE_MOCK_SERVER ? MOCK_PLANNER_HOST : plannerHost;
    console.log(`🔧 调试模式: ${USE_MOCK_SERVER ? 'Mock服务器' : '真实服务器'}`);
    console.log(`📍 目标地址: ${targetHost}/gbi/planner/service/regenerate_summary`);

    try {
      const plannerResponse = await axios.post(`${targetHost}/gbi/planner/service/regenerate_summary`, plannerRequestData);

      if (plannerResponse.status === 200 && plannerResponse.data.code === '0') {
        // 成功响应，返回planner返回的summaryKey
        const summaryKey = plannerResponse.data.data.summaryKey;
        console.log(`✅ planner响应成功 - summaryKey: ${summaryKey}`);
        const response: IRegenerateSummaryResponse = {
          status: 200,
          message: '重新生成总结请求已提交',
          data: {
            key: summaryKey
          }
        };
        res.sugarJson(response);
      } else {
        console.error(`❌ planner接口调用失败:`, plannerResponse.data);
        throw new Error(`planner接口调用失败: ${plannerResponse.data.msg || '未知错误'}`);
      }
    } catch (plannerError) {
      console.error('planner接口调用失败:', plannerError);
      throw new Error('调用总结生成服务失败');
    }
  } catch (error) {
    console.error('重新生成总结失败:', error);
    res.sugarErrorJson('重新生成总结失败，请稍后重试');
  }
};

/**
 * 查询总结内容接口
 * @param req
 * @param res
 * @param next
 */
export const querySummaryContent = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('🔍 收到查询总结内容请求');
    const {key} = req.query;
    console.log(`🔑 查询参数 - summaryKey: ${key}`);

    if (!key || typeof key !== 'string') {
      return res.sugarErrorJson('参数错误：key不能为空');
    }

    if (!plannerHost) {
      throw new CommonError('未配置planner地址');
    }

    // 直接调用planner接口查询总结内容
    // 根据调试开关选择使用mock服务器还是真实服务器

    const targetHost = USE_MOCK_SERVER ? MOCK_PLANNER_HOST : plannerHost;
    console.log(`🔍 查询总结内容 - 目标地址: ${targetHost}/gbi/planner/service/get_summary`);
    console.log(`🔑 summaryKey: ${key}`);

    const requestData = { summaryKey: key };
    console.log('📤 发送请求数据:', JSON.stringify(requestData, null, 2));

    try {
      const plannerResponse = await axios.post(`${targetHost}/gbi/planner/service/get_summary`, requestData);
      console.log('📥 收到planner原始响应:', JSON.stringify(plannerResponse.data, null, 2));

      if (plannerResponse.status === 200 && plannerResponse.data.code === '0') {
        // 新的响应格式：直接在data下，不是data.data
        const responseData = plannerResponse.data.data;
        console.log(`📊 planner查询响应 - status: ${responseData.status}, 片段数: ${responseData.result?.length || 0}`);
        console.log('🔍 完整响应数据:', JSON.stringify(responseData, null, 2));

        if (responseData.status === 'end' && responseData.result) {
          console.log('🎯 检测到总结生成完成，开始处理最终结果...');
          // 参考项目中已有的流式数据处理逻辑
          // 将流式数据片段组合成完整内容，按sentence_id排序
          const content = responseData.result
            .sort((a: any, b: any) => a.sentence_id - b.sentence_id)
            .map((item: any) => item.content || '')
            .join('');

          console.log(`✅ 总结生成完成 - 内容长度: ${content.length}`);
          const finalResponse = {
            status: 200,
            message: '查询成功',
            data: {
              content: content,
              status: 'completed',
              id: responseData.id,
              // 保留原始流式数据，以便前端可以做流式展示
              streamResult: responseData.result
            }
          };
          console.log('📤 返回给前端的最终响应:', JSON.stringify(finalResponse, null, 2));
          res.sugarJson(finalResponse);
        } else {
          // 总结还在生成中，返回当前状态和已有的部分数据
          let partialContent = '';
          if (responseData.result && Array.isArray(responseData.result)) {
            partialContent = responseData.result
              .sort((a: any, b: any) => a.sentence_id - b.sentence_id)
              .map((item: any) => item.content || '')
              .join('');
          }

          res.sugarJson({
            status: 202,
            message: '总结生成中，请稍后查询',
            data: {
              status: responseData.status || 'processing',
              partialContent: partialContent,
              streamResult: responseData.result || []
            }
          });
        }
      } else {
        throw new Error(`planner接口调用失败: ${plannerResponse.data.msg || '未知错误'}`);
      }
    } catch (plannerError) {
      console.error('planner接口调用失败:', plannerError);
      // 如果是网络错误或服务不可用，返回202让前端继续轮询
      if (plannerError.code === 'ECONNREFUSED' || plannerError.code === 'ETIMEDOUT') {
        res.sugarJson({
          status: 202,
          message: '总结生成中，请稍后查询',
          data: null
        });
      } else {
        throw new Error('查询总结内容失败');
      }
    }
  } catch (error) {
    console.error('查询总结内容失败:', error);
    res.sugarErrorJson('查询总结内容失败，请稍后重试');
  }
};
